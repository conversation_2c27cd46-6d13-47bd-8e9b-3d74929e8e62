<?php

namespace App\Models;

use App\Services\DatabaseService;

/**
 * Model untuk data BPJS
 */
class BpjsData
{
    private $db;

    public function __construct(DatabaseService $db)
    {
        $this->db = $db;
    }

    /**
     * Simpan data peserta BPJS
     */
    public function savePeserta($data)
    {
        $pesertaData = [
            'norm' => $data['norm'],
            'noKartu' => $data['noKartu'],
            'nama' => $data['nama'],
            'nik' => $data['nik'] ?? '',
            'tglLahir' => $data['tglLahir'] ?? '',
            'jenisKelamin' => $data['jenisKelamin'] ?? '',
            'statusPeserta' => $data['statusPeserta'] ?? '',
            'hakKelas' => $data['hakKelas'] ?? '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Cek apakah data sudah ada
        $existing = $this->findPesertaByNoKartu($data['noKartu']);
        
        if ($existing) {
            // Update data yang sudah ada
            unset($pesertaData['created_at']);
            return $this->db->update('bpjs.peserta', $pesertaData, 'noKartu = ?', [$data['noKartu']]);
        } else {
            // Insert data baru
            return $this->db->insert('bpjs.peserta', $pesertaData);
        }
    }

    /**
     * Cari peserta berdasarkan nomor kartu
     */
    public function findPesertaByNoKartu($noKartu)
    {
        $sql = "SELECT * FROM bpjs.peserta WHERE noKartu = ?";
        return $this->db->fetchOne($sql, [$noKartu]);
    }

    /**
     * Cari peserta berdasarkan NORM
     */
    public function findPesertaByNorm($norm)
    {
        $sql = "SELECT * FROM bpjs.peserta WHERE norm = ?";
        return $this->db->fetchOne($sql, [$norm]);
    }

    /**
     * Simpan data rujukan
     */
    public function saveRujukan($data)
    {
        $rujukanData = [
            'noKartu' => $data['noKartu'],
            'noRujukan' => $data['noRujukan'],
            'tglRujukan' => $data['tglRujukan'],
            'ppkRujukan' => $data['ppkRujukan'],
            'diagnosa' => $data['diagnosa'] ?? '',
            'poliRujukan' => $data['poliRujukan'] ?? '',
            'asalRujukan' => $data['asalRujukan'] ?? '2',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Cek apakah rujukan sudah ada
        $existing = $this->findRujukanByNoRujukan($data['noRujukan']);
        
        if ($existing) {
            // Update data yang sudah ada
            unset($rujukanData['created_at']);
            return $this->db->update('bpjs.rujukan', $rujukanData, 'noRujukan = ?', [$data['noRujukan']]);
        } else {
            // Insert data baru
            return $this->db->insert('bpjs.rujukan', $rujukanData);
        }
    }

    /**
     * Cari rujukan berdasarkan nomor rujukan
     */
    public function findRujukanByNoRujukan($noRujukan)
    {
        $sql = "SELECT * FROM bpjs.rujukan WHERE noRujukan = ?";
        return $this->db->fetchOne($sql, [$noRujukan]);
    }

    /**
     * Cari rujukan berdasarkan nomor kartu
     */
    public function findRujukanByNoKartu($noKartu)
    {
        $sql = "SELECT * FROM bpjs.rujukan WHERE noKartu = ? ORDER BY tglRujukan DESC LIMIT 1";
        return $this->db->fetchOne($sql, [$noKartu]);
    }

    /**
     * Simpan data SEP
     */
    public function saveSep($data)
    {
        $sepData = [
            'noSEP' => $data['noSEP'],
            'noKartu' => $data['noKartu'],
            'norm' => $data['norm'],
            'tglSEP' => $data['tglSEP'],
            'jnsPelayanan' => $data['jnsPelayanan'],
            'klsRawat' => $data['klsRawat'],
            'noRujukan' => $data['noRujukan'],
            'ppkRujukan' => $data['ppkRujukan'],
            'diagAwal' => $data['diagAwal'],
            'poliTujuan' => $data['poliTujuan'],
            'dpjp' => $data['dpjp'],
            'noKontrol' => $data['noKontrol'] ?? '',
            'user' => $data['user'] ?? '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->insert('bpjs.sep', $sepData);
    }

    /**
     * Cari SEP berdasarkan nomor SEP
     */
    public function findSepByNoSep($noSEP)
    {
        $sql = "SELECT * FROM bpjs.sep WHERE noSEP = ?";
        return $this->db->fetchOne($sql, [$noSEP]);
    }

    /**
     * Cari SEP berdasarkan NORM dan tanggal
     */
    public function findSepByNormAndDate($norm, $tanggal = null)
    {
        $tanggal = $tanggal ?: date('Y-m-d');
        $sql = "SELECT * FROM bpjs.sep WHERE norm = ? AND DATE(tglSEP) = ? ORDER BY created_at DESC LIMIT 1";
        return $this->db->fetchOne($sql, [$norm, $tanggal]);
    }

    /**
     * Get kunjungan BPJS terakhir
     */
    public function getLastBpjsVisit($norm)
    {
        $sql = "SELECT bpkj.jenisPelayanan, bpkj.noSEP noSEPRanap, bpkj.dpjp 
                FROM bpjs.peserta bps 
                LEFT JOIN bpjs.kunjungan bpkj ON bps.noKartu = bpkj.noKartu
                WHERE bps.norm = ? AND bpkj.errMsgBatalSEP IS NULL 
                ORDER BY bpkj.tglSEP DESC LIMIT 1";
        
        return $this->db->fetchOne($sql, [$norm]);
    }

    /**
     * Validasi masa berlaku rujukan
     */
    public function validateRujukanExpiry($tglRujukan, $maxDays = 90)
    {
        $dateRujukan = new \DateTime($tglRujukan);
        $dateNow = new \DateTime();
        $diff = $dateNow->diff($dateRujukan)->days;

        return [
            'valid' => $diff <= $maxDays,
            'days_diff' => $diff,
            'max_days' => $maxDays
        ];
    }

    /**
     * Convert hak kelas BPJS ke ID
     */
    public function convertHakKelasToId($hakKelas)
    {
        $mapping = [
            'KELAS III' => 3,
            'KELAS II' => 2,
            'KELAS I' => 1
        ];

        return $mapping[$hakKelas] ?? 3; // Default ke kelas 3
    }

    /**
     * Get statistik penggunaan BPJS
     */
    public function getBpjsStats($startDate = null, $endDate = null)
    {
        $startDate = $startDate ?: date('Y-m-01'); // Awal bulan
        $endDate = $endDate ?: date('Y-m-d'); // Hari ini

        $sql = "SELECT 
                    COUNT(*) as total_sep,
                    COUNT(DISTINCT norm) as unique_patients,
                    jnsPelayanan,
                    klsRawat
                FROM bpjs.sep 
                WHERE DATE(tglSEP) BETWEEN ? AND ?
                GROUP BY jnsPelayanan, klsRawat";

        return $this->db->fetchAll($sql, [$startDate, $endDate]);
    }
}
