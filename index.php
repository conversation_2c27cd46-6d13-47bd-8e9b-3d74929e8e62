<!DOCTYPE html>
<html>	
<head>
<title>Pendaftaran Mandiri RSKD</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="utf-8">
<meta name="keywords" content="Doctor Availability Form Bootstrap Responsive Templates, Iphone Compatible Templates, Smartphone Compatible Templates, Ipad Compatible Templates, Flat Responsive Templates"/>
<link rel="stylesheet" href="css/jquery-ui.css" type="text/css" media="all">
<link href="css/wickedpicker.css" rel="stylesheet" type='text/css' media="all" />
<link href="css/style.css" rel='stylesheet' type='text/css' />
<!--webfonts-->
<link href="css/font_css.css" rel="stylesheet">
<link rel="stylesheet" href="css/bootstrap.min.css">
<!--//webfonts-->

<style>
#calc{width:100%;height:100%;}
#btn{width:100%;height:40px;font-size:20px;}
table { 
    border-spacing: 10px;
    border-collapse: separate;
}
</style>
</head>
<br>

		<!-- <h1 style="color: #008000;">SELAMAT DATANG DI RUMAH SAKIT "KANKER" DHARMAIS</h1> -->
		<h1><span style="background:hsla(0, 0.00%, 100.00%, 0.5);font-size:80px;padding:16px;color:#267b84;font-weight: bold;">KIOSK |<img src="images/logo.png" style="width: 18%;"></span></h1><br/>
		<div class="containerw3layouts-agileits" style="width: 600px;">
			<div class="w3layoutscontactagileits">				
				<div id="wrapper">
					<form name="calc" action="daftar.php" method="post" autocomplete="off">
						<h2>Masukkan No MR Anda</h2>
						<div id="login" class="animate w3layouts agileits form">
						
						<div class="ferry ferry-from">
							<table id='calc' border=2>
								<tr>
								<td colspan=5>
									<input id="nomr" name="nomr" onkeypress="return event.charCode >= 48 && event.charCode <= 57" type="text" required=" " style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;" autofocus></td>
								<td style="display:none"><input name="M" type="number"></td>
								<input style="display:none" type="key" name="key" value="crtghb68p2xb6jc44gcdx9jhm">
								</tr>
								<tr>
									<td><input id="btn" type=button value="1" OnClick="calc.nomr.value+='1'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
									<td><input id="btn" type=button value="2" OnClick="calc.nomr.value+='2'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
									<td><input id="btn" type=button value="3" OnClick="calc.nomr.value+='3'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
								</tr>
								<tr>
									<td><input id="btn" type=button value="4" OnClick="calc.nomr.value+='4'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
									<td><input id="btn" type=button value="5" OnClick="calc.nomr.value+='5'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
									<td><input id="btn" type=button value="6" OnClick="calc.nomr.value+='6'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
								</tr>
								<tr>
									<td><input id="btn" type=button value="7" OnClick="calc.nomr.value+='7'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
									<td><input id="btn" type=button value="8" OnClick="calc.nomr.value+='8'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
									<td><input id="btn" type=button value="9" OnClick="calc.nomr.value+='9'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
								</tr>
								<tr>
								    <!-- <td><input id="btn" type=button value="C" OnClick="calc.nomr.value=''" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td> -->
									<td></td>
									<td><input id="btn" type=button value="0" OnClick="calc.nomr.value+='0'" class="btn btn-warning" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
								    <td><input id="btn" type=button value="" OnClick="back();" class="btn btn-warning" style="background-image: url(images/backspace.png); background-size: 100% 100%; background-repeat: no repeat; text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></td>
								</tr>
								</table>
						</div>
						<br>
						<br>
						<div class="wthreesubmitaits">
							<input type="submit" name="submit" value="Cari" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;">
							<!-- <input type="submit" name="submit" value="Clear" OnClick="calc.display.value=''"> -->
						</div>

						</div>
					</form>
				</div>
			</div>
		</div>
		<div class="w3lsfooteragileits">
			<p style="color: #008000;"> &copy; SIMRS 2018 Aplikasi Pendaftaran Mandiri. All Rights Reserved </p>
		</div>
		<!-- Necessary-JavaScript-Files-&-Links -->
			<!-- Date-Picker-JavaScript -->
<script type="text/javascript" src="js/jquery-2.1.4.min.js"></script>
<script  src="js/jquery-ui.js"></script>	
<script  src="js/wickedpicker.js"></script>	
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
<script type="text/javascript">
	$(function() {
		$( "#datepicker,#datepicker1,#datepicker2" ).datepicker();
	});
</script>

<script type="text/javascript">
$('.timepicker').wickedpicker({twentyFour: false});

function back() {
    var value = document.getElementById("nomr").value;
    document.getElementById("nomr").value = value.substr(0, value.length - 1);
}
</script>

<!-- //Date-Picker-JavaScript -->
<!-- //Necessary-JavaScript-Files-&-Links -->
	

</body>
</html>