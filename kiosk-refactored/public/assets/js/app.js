/**
 * Kiosk Application JavaScript
 * Main application logic for RSKD Kiosk
 */

(function($) {
    'use strict';

    // Application namespace
    window.KioskApp = {
        config: {
            ajaxTimeout: 30000,
            autoRedirectDelay: 3000,
            maxMrLength: 10,
            printTimeout: 10000
        },

        // Initialize application
        init: function() {
            this.bindEvents();
            this.initComponents();
            this.setupAjax();
        },

        // Bind global events
        bindEvents: function() {
            // Prevent form double submission
            $('form').on('submit', function() {
                var $form = $(this);
                var $submitBtn = $form.find('button[type="submit"]');
                
                if ($submitBtn.prop('disabled')) {
                    return false;
                }
                
                $submitBtn.prop('disabled', true);
                KioskApp.showLoading();
                
                // Re-enable after 5 seconds as fallback
                setTimeout(function() {
                    $submitBtn.prop('disabled', false);
                    KioskApp.hideLoading();
                }, 5000);
            });

            // Auto-hide alerts
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            // Handle AJAX errors globally
            $(document).ajaxError(function(event, xhr, settings, error) {
                KioskApp.hideLoading();
                
                if (xhr.status === 0) {
                    KioskApp.showAlert('error', 'Koneksi Error', 'Tidak dapat terhubung ke server');
                } else if (xhr.status === 404) {
                    KioskApp.showAlert('error', 'Not Found', 'Halaman tidak ditemukan');
                } else if (xhr.status === 500) {
                    KioskApp.showAlert('error', 'Server Error', 'Terjadi kesalahan server');
                } else {
                    KioskApp.showAlert('error', 'Error', 'Terjadi kesalahan: ' + error);
                }
            });
        },

        // Initialize components
        initComponents: function() {
            // Initialize date pickers
            if ($.fn.datepicker) {
                $('.datepicker').datepicker({
                    dateFormat: 'dd/mm/yy',
                    changeMonth: true,
                    changeYear: true
                });
            }

            // Initialize time pickers
            if ($.fn.wickedpicker) {
                $('.timepicker').wickedpicker({
                    twentyFour: true,
                    title: 'Pilih Waktu'
                });
            }

            // Initialize tooltips
            if ($.fn.tooltip) {
                $('[data-toggle="tooltip"]').tooltip();
            }

            // Initialize popovers
            if ($.fn.popover) {
                $('[data-toggle="popover"]').popover();
            }
        },

        // Setup AJAX defaults
        setupAjax: function() {
            $.ajaxSetup({
                timeout: this.config.ajaxTimeout,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
        },

        // Show loading overlay
        showLoading: function() {
            $('#loadingOverlay').show();
        },

        // Hide loading overlay
        hideLoading: function() {
            $('#loadingOverlay').hide();
        },

        // Show alert using SweetAlert
        showAlert: function(type, title, message, timer) {
            timer = timer || 3000;
            
            if (typeof swal !== 'undefined') {
                swal({
                    icon: type,
                    title: title,
                    text: message,
                    timer: timer,
                    button: false
                });
            } else {
                // Fallback to browser alert
                alert(title + ': ' + message);
            }
        },

        // Show confirmation dialog
        showConfirm: function(title, message, callback) {
            if (typeof swal !== 'undefined') {
                swal({
                    title: title,
                    text: message,
                    icon: 'warning',
                    buttons: {
                        cancel: {
                            text: 'Batal',
                            value: false,
                            visible: true
                        },
                        confirm: {
                            text: 'Ya',
                            value: true,
                            visible: true
                        }
                    }
                }).then(function(result) {
                    if (callback && typeof callback === 'function') {
                        callback(result);
                    }
                });
            } else {
                // Fallback to browser confirm
                var result = confirm(title + '\n' + message);
                if (callback && typeof callback === 'function') {
                    callback(result);
                }
            }
        },

        // Validate MR number
        validateMr: function(mr) {
            if (!mr || mr.length === 0) {
                return { valid: false, message: 'Nomor MR tidak boleh kosong' };
            }

            if (mr.length > this.config.maxMrLength) {
                return { valid: false, message: 'Nomor MR terlalu panjang' };
            }

            if (!/^[0-9]+$/.test(mr)) {
                return { valid: false, message: 'Nomor MR hanya boleh berisi angka' };
            }

            return { valid: true, message: 'Valid' };
        },

        // Format currency
        formatCurrency: function(amount) {
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
        },

        // Format date
        formatDate: function(date, format) {
            format = format || 'dd/mm/yyyy';
            
            if (!date) return '-';
            
            try {
                var d = new Date(date);
                var day = ('0' + d.getDate()).slice(-2);
                var month = ('0' + (d.getMonth() + 1)).slice(-2);
                var year = d.getFullYear();
                
                return format
                    .replace('dd', day)
                    .replace('mm', month)
                    .replace('yyyy', year);
            } catch (e) {
                return date;
            }
        },

        // Redirect with delay
        redirectWithDelay: function(url, delay) {
            delay = delay || this.config.autoRedirectDelay;
            
            setTimeout(function() {
                window.location.href = url;
            }, delay);
        },

        // Print functionality
        print: function(options) {
            options = options || {};
            
            if (typeof window.requestPrint === 'function') {
                window.requestPrint({
                    NAME: options.name || 'default',
                    TYPE: options.type || 'Word',
                    EXT: options.ext || 'docx',
                    PARAMETER: options.parameters || {},
                    REQUEST_FOR_PRINT: options.print !== false,
                    PRINT_NAME: options.printName || 'Document',
                    CONNECTION_NUMBER: options.connection || 0,
                    COPIES: options.copies || 1,
                    id: options.id || 'print-request'
                });

                // Auto redirect after print
                if (options.redirectUrl) {
                    setTimeout(function() {
                        window.location.href = options.redirectUrl;
                    }, options.redirectDelay || this.config.printTimeout);
                }
            } else {
                this.showAlert('error', 'Print Error', 'Fungsi print tidak tersedia');
            }
        },

        // Utility functions
        utils: {
            // Sanitize HTML
            sanitizeHtml: function(str) {
                var div = document.createElement('div');
                div.textContent = str;
                return div.innerHTML;
            },

            // Generate random string
            randomString: function(length) {
                length = length || 10;
                var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                var result = '';
                for (var i = 0; i < length; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            },

            // Check if mobile device
            isMobile: function() {
                return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            },

            // Get URL parameter
            getUrlParameter: function(name) {
                name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                var results = regex.exec(location.search);
                return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        KioskApp.init();
    });

})(jQuery);
