<!DOCTYPE html>
<html>	
<head>
<title>Pendaftaran Mandiri RSKD</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="utf-8">

<link rel="stylesheet" href="css/jquery-ui.css" type="text/css" media="all">
<link href="css/wickedpicker.css" rel="stylesheet" type='text/css' media="all" />
<link href="css/style.css" rel='stylesheet' type='text/css' />
<!--webfonts-->
<link href="css/font.css" rel="stylesheet">
<link rel="stylesheet" href="css/sweetalert.min.css">
<link rel="stylesheet" href="css/bootstrap.min.css"  media="all">
<script src="js/sweetalert.min.js"></script>

<!-- <script src="js/jquery-2.1.4.min.js"></script> -->
<script src="js/modernizr.js"></script>
<!--//webfonts-->
<script src="assets/printdoc.js"></script>
<style>
	#calc{width:100%;height:100%;}
	#btn{width:100%;height:40px;font-size:20px;}

	.no-js #loader { display: none;  }
	.js #loader { display: block; position: absolute; left: 100px; top: 0; }
	.se-pre-con {
		position: fixed;
		left: 0px;
		top: 0px;
		width: 100%;
		height: 100%;
		z-index: 9999;
		background: url(images/loader-128x/Preloader_3.gif) center no-repeat #fff;
	}
	
	.swal-wide{
		width:850px !important;
		height:850px !important;
	}
</style>

</head>
<body>
<!-- <div class="se-pre-con"></div> -->
<?php
include "koneksi.php";
include "Service.php";
include "BpjsService.php";

$service = new Service();
$bpjsService = new BpjsService();

if($_SERVER['REQUEST_METHOD']=="POST"){
	if(isset($_POST['nomr'])){ 

	$mr = $_POST['nomr'];
	if($_POST['key']=="crtghb68p2xb6jc44gcdx9jhm" && $mr != null){

    $sql_kartu = "SELECT pd.RUJUKAN rujukan, kap.NOMOR kartubpjs
					FROM pendaftaran.pendaftaran pd
					LEFT JOIN `master`.kartu_asuransi_pasien kap ON pd.NORM = kap.NORM AND kap.JENIS = 2 AND LENGTH(kap.NOMOR) = 13
					WHERE pd.NORM=$mr AND pd.RUJUKAN IS NOT NULL
					ORDER BY pd.TANGGAL DESC
					LIMIT 1";
    $query_kartu = mysqli_query($conn, $sql_kartu);
		
		if($query_kartu){
		  // echo $sql_kartu;die();
			$count = mysqli_num_rows($query_kartu);
			// echo $count;die();

			if($count < 1) { ?>
				<script type='text/javascript'>
				document.addEventListener("DOMContentLoaded", function(event) {
					swal({            
					icon : 'error',
					title : 'Maaf!',
					text: 'Tidak bisa didaftarkan, bukan pasien BPJS.',
					timer : 3000,
					button : false
					}).then(function() {
					window.location = "index.php";
					});
				});
				</script>
			<?php
			} //bukan pasien bpjs
			else{
				$sql_daftar = "SELECT pp.NOMOR,pp.NORM,pp.TANGGAL,tp.RUANGAN,pj.NOMOR SEP FROM pendaftaran.pendaftaran pp 
									LEFT JOIN pendaftaran.penjamin pj ON pp.NOMOR=pj.NOPEN AND pj.JENIS=2
									LEFT JOIN pendaftaran.tujuan_pasien tp ON pp.NOMOR=tp.NOPEN
								WHERE pp.NORM='$mr' AND pp.`STATUS`='1' AND DATE(TANGGAL) = DATE(NOW())";

				$query_daftar = mysqli_query($conn, $sql_daftar);
				$row_daftar = mysqli_fetch_array($query_daftar);
				$hitung_daftar = mysqli_num_rows($query_daftar);
				$isi3 = "Terdapat Tagihan Yang Masih Aktif";

				if($hitung_daftar > 0){ 
					//jika terdapat tagihan belum difinalkan
					
					$insert_logApm = "INSERT INTO `log`.`log_apmhelpdesk`(`nomr`, `tanggal`) 
										VALUES ('$mr', NOW());";
					$query_apm = mysqli_query($conn, $insert_logApm);
      		?> 	
					<h1><span style="background:hsla(0, 0.00%, 100.00%, 0.5);font-size:80px;padding:16px;color:#267b84;font-weight: bold;">KIOSK |<img src="images/logo.png" style="width: 18%;"></span></h1><br/>
					<div class="row">
						<div class="col-md-2" align="right"></div>
						<div class="col-md-8" align="center">
							<div class="containerw3layouts-agileits" style="width: 600px;">
								<div class="w3layoutscontactagileits">	
									<div id="wrapper">
										<input type="text" name="nosep" readonly="readonly" value="<?= $row_daftar['SEP']; ?>" hidden/>
										<embed type="text/html" src="cetakSep.php?sep=<?= $row_daftar['SEP'];?>"  width="500" height="420">
										<?php
											if($row_daftar['RUANGAN'] != '105120101') {
										?>
												<input type="button" class="btn btn-success" value="Print" onclick="print(1)">
												<?php
											}else{
												?>
												<p class="text-danger" style="text-align: center; font-size: 150%; font-weight: bold; background-color: #d4edda; border-radius: 10px; padding: 10px;">Kiosk sudah tidak mencetak Bukti Registrasi</p>
												<?php
											}
											?>
											<a type="button" class="btn btn-danger" href="index.php">Kembali</a>
									</div>
								</div>
							</div>
						</div>
					</div>
					<script type='text/javascript'>
						function print(flag) {
							var sep = '<?= $row_daftar['SEP']; ?>';
							var cetak = "bpjs.CetakSEP_1";
							if (flag == 1) {
								type = "Word";
								ext = "docx";
								print = true;
							}else {
								type = "Pdf";
								ext = "pdf";
								print = false;
							}
							window.requestPrint({
								NAME:cetak,
								TYPE:type,
								EXT:ext,
								PARAMETER:{
									PSEP:sep,CETAK_HEADER:1
								},
								REQUEST_FOR_PRINT:print,
								PRINT_NAME:"CetakSEP",
								CONNECTION_NUMBER:0,
								COPIES:1,
								id:"data.model.RequestReport-1"
							});
							setTimeout(() => {
								window.location = "index.php";
							}, 10000);
						}
					</script>
				<?php
				}else{
					//cek masih terdapat tagihan yg belum di final
					$sql2 = "SELECT * FROM pembayaran.tagihan 
						 WHERE pembayaran.tagihan.REF='$mr' AND pembayaran.tagihan.`STATUS`='1'";

					$query2 = mysqli_query($conn, $sql2);
					$hitung2 = mysqli_num_rows($query2);
					$isi3 = "Terdapat Tagihan Yang Masih Aktif";

					if($hitung2 > 0){ 
						//jika terdapat tagihan belum difinalkan
						$row_dataTagihan = mysqli_fetch_assoc($query2);
						$idtagihan = $row_dataTagihan['ID'];
						
						$insert_logApm = "INSERT INTO `log`.`log_apmhelpdesk`(`nomr`, `tanggal`, `tagihan`) 
											VALUES ('$mr', NOW(), '$idtagihan');";
						$query_apm = mysqli_query($conn, $insert_logApm);
						
				?> 
						<script type='text/javascript'>
							document.addEventListener("DOMContentLoaded", function(event) {
								swal({
								icon : 'error',
								title : 'Maaf!',
								text: "Anda Belum bisa mendaftar. Silakan ambil antrian.",
								timer: 5000,
								button : false
								}).then(function() {
								window.location = "index.php";
								});
							});
						</script>
					<?php
					}else{
						$sql_rencana = "SELECT * FROM remun_medis.perjanjian rp
						WHERE rp.TANGGAL = DATE(NOW()) AND rp.NOMR = '$mr' AND rp.STATUS != 0
						AND rp.ID_RUANGAN NOT IN ('105020201')";
						$query_rencana = mysqli_query($conn, $sql_rencana);
						$row_rencana = mysqli_fetch_array($query_rencana);
						$rencanaTanpaSep = array(4);
						$row_kartu = mysqli_fetch_array($query_kartu);
						if($error == 0 && !in_array($row_rencana['RENCANA'],$rencanaTanpaSep)) {
							$kartubpjs = $row_kartu['kartubpjs'];
							//cek kepesertaan
							// $result = $service->cariPesertaDgnNoKartuBPJS($kartubpjs, date("Y-m-d"));
							$result = $bpjsService->request('vclaimv2/peserta', ['noKartu' => $kartubpjs, 'tglSEP' => date("Y-m-d")], 'GET');

							// echo json_encode($result,JSON_PRETTY_PRINT);die();


							$statusPeserta = $result->response->peserta->statusPeserta->keterangan;
							$hakKelas = $result->response->peserta->hakKelas->keterangan;
							
							if($statusPeserta == "AKTIF"){ //jika aktif lanjutkan

							// $result = $service->cariRujukanDgnNoKartuBPJS($kartubpjs);
								$result = $bpjsService->request('vclaimv2/rujukan/rs', ['noKartu' => $kartubpjs], 'GET');


								// echo json_encode($result,JSON_PRETTY_PRINT);die();

								if($result->metaData->code != 200){ //jika rujukan faskes 2 kosong
									$result2 = $bpjsService->request('vclaimv2/rujukan/pcare', ['noKartu' => $kartubpjs], 'GET');

									$message_faskes1 = $result2->response->rujukan[0]->tglKunjungan;

									// echo json_encode($message_faskes1,JSON_PRETTY_PRINT);die();

									if($message_faskes1 === NULL){
									?>
									<script type='text/javascript'>
										var status = "<?php echo $statusPeserta; ?>";
										document.addEventListener("DOMContentLoaded", function(event) {
											swal({
													icon : 'warning',
													title : 'Maaf!',
													text: "Masa berlaku rujukan sudah habis (Faskes 1)",
													timer: 5000,
													button : false
												}).then(function() {
												window.location = "index.php";
											});
										});

									</script>
									<?php
									
									}else{
										$diagawal = $result2->response->rujukan[0]->diagnosa->kode;
										$jenisLayanan = $result2->response->rujukan[0]->pelayanan->kode;
										$ppkRujukan = $result2->response->rujukan[0]->provPerujuk->kode;
										$noTelp = $result2->response->rujukan[0]->peserta->mr->noTelepon;
										$noRujukan = $result2->response->rujukan[0]->noKunjungan;
										$tglRujukan = $result2->response->rujukan[0]->tglKunjungan;
										$poliRujukan = $result2->response->rujukan[0]->poliRujukan->kode;

										$date_faskes1 = new DateTime($result2->response->rujukan[0]->tglKunjungan);
										
										$date_faskes2 = new DateTime();

										$diff = $date_faskes2->diff($date_faskes1)->format("%a");

										if($diff > 90){ //jika rujukan > 90 hari
											?>
											<script type='text/javascript'>
												var status = "<?php echo $statusPeserta; ?>";
												document.addEventListener("DOMContentLoaded", function(event) {
													swal({
															icon : 'warning',
															title : 'Maaf!',
															text: "Masa berlaku rujukan sudah lebih dari 3 bulan.",
															timer: 5000,
															button : false
														}).then(function() {
														window.location = "index.php";
													});
												});

											</script>
											<?php
										}
									}
								}else{ //else ada rujukan faskes 2
									$diagawal = $result->response->rujukan[0]->diagnosa->kode;
									$jenisLayanan = $result->response->rujukan[0]->pelayanan->kode;
									$ppkRujukan = $result->response->rujukan[0]->provPerujuk->kode;
									$noTelp = $result->response->rujukan[0]->peserta->mr->noTelepon;
									$noRujukan = $result->response->rujukan[0]->noKunjungan;
									$tglRujukan = $result->response->rujukan[0]->tglKunjungan;
									$poliRujukan = $result->response->rujukan[0]->poliRujukan->kode;

									$date1 = new DateTime($result->response->rujukan[0]->tglKunjungan);
									$date2 = new DateTime();
									$diff = $date2->diff($date1)->format("%a");
					
									if($diff > 90){ //jika rujukan > 90 hari
								?>
									<script type='text/javascript'>
										var status = "<?php echo $statusPeserta; ?>";
											document.addEventListener("DOMContentLoaded", function(event) {
											swal({
												icon : 'warning',
												title : 'Maaf!',
												text: "Masa berlaku rujukan sudah habis (Faskes 2).",
												timer: 5000,
												button : false
												}).then(function() {
												window.location = "index.php";
											});
										});
									</script>
								<?php
									}
								}

								//cek perjanjian
								$sql_perjanjian = "SELECT NOMR, NAMAPASIEN, ID_DOKTER
										,master.getNamalengkapPegawai(mdok.NIP) NAMADOKTER
										, mdok.HAFIS, rmp.TANGGAL TGLPERJANJIAN, NOKONTROL
										, ID_RUANGAN,  mr.DESKRIPSI RUANGAN, mpeg.SMF
										, pr.RUANGAN_PENJAMIN, pb.nmsubspesialis NAMAPOLI
										#, (SELECT RUANGAN_PENJAMIN FROM master.penjamin_ruangan WHERE RUANGAN_RS = mpeg.SMF AND STATUS=1) RUANGAN_PENJAMIN
										, IF(STR_TO_DATE(rmj.AWAL - 1, '%H:%i:%s') < CURRENT_TIME() 
										, IF(STR_TO_DATE(rmj.AKHIR , '%H:%i:%s') - INTERVAL '30' MINUTE  > CURRENT_TIME(),1,1)
										, 2) WAKTU_MULAI
										, rmj.AWAL
										, rmj.AKHIR
										, rmp.RENCANA
										, IF (
											STR_TO_DATE(rmj.AWAL, '%H:%i:%s') - INTERVAL 30 MINUTE < CURRENT_TIME()
											AND STR_TO_DATE(rmj.AKHIR, '%H:%i:%s') - INTERVAL 30 MINUTE > CURRENT_TIME(),
											1,
											0
										) AS `LOCK` #aktif lock 1,0
									FROM remun_medis.perjanjian_copy rmp 
									LEFT JOIN master.dokter mdok ON mdok.ID = rmp.ID_DOKTER
									LEFT JOIN master.pegawai mpeg ON mpeg.NIP = mdok.NIP AND mdok.`STATUS` != 0
									LEFT JOIN `master`.penjamin_ruangan pr ON mpeg.SMF = pr.RUANGAN_RS
									LEFT JOIN `master`.poli_bpjs_2 pb ON pr.RUANGAN_PENJAMIN = pb.kdsubspesialis
									LEFT JOIN remun_medis.jadwal rmj ON rmj.DOKTER = rmp.ID_DOKTER AND rmj.RUANGAN = rmp.ID_RUANGAN AND rmj.TANGGAL = rmp.TANGGAL AND rmj.`STATUS` != 0
									LEFT JOIN remun_medis.jadwal_detail jd ON rmp.SLOT=jd.ID_JADWAL
									LEFT JOIN `master`.ruangan mr ON rmp.ID_RUANGAN = mr.ID
									WHERE rmp.TANGGAL = DATE(NOW()) AND NOMR = $mr AND rmp.STATUS != 0 AND rmp.DPJP != 0
									AND rmp.ID_RUANGAN NOT IN ('105020201')
									#AND rmp.ID_RUANGAN IN ('105020704','105020705','105120101','105060101','105110101', '105020401')
									AND mr.GEDUNG IS NULL
									ORDER BY NOKONTROL ASC LIMIT 1";
									// echo die($sql_perjanjian);
								$query_perjanjian = mysqli_query($conn, $sql_perjanjian);
								if($query_perjanjian){
								?>
									<!-- <h1 style="color: #008000;">SILAKAN PILIH POLI TUJUAN ANDA</h1> -->
									<h1><span style="background:hsla(0, 0.00%, 100.00%, 0.5);font-size:80px;padding:16px;color:#267b84;font-weight: bold;">KIOSK |<img src="images/logo.png" style="width: 18%;"></span></h1><br/>

									<div class="row">
										<div class="col-md-2" align="right"><input type="button" class="btn btn-success" value="Back" onclick="goBack()" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></div>
										<div class="col-md-8" align="center">
											<div class="containerw3layouts-agileits" style="width: 600px;">
												<div class="w3layoutscontactagileits">	
													<div id="wrapper">
														<form name="calc" action="dataSep.php" method="post" id="calc">
														<input type="text" name="sep" readonly="readonly" value="1" hidden/>
															<?php
															$rowcount=mysqli_num_rows($query_perjanjian);
															if($rowcount > 0){
																while($row_perjanjian = mysqli_fetch_assoc($query_perjanjian)){
																	$hafisSekarang = $row_perjanjian['HAFIS'];
																	$nokontrol = $row_perjanjian['NOKONTROL'];
																	$poliTujuan = $row_perjanjian['RUANGAN_PENJAMIN'];
																	$namaPoliTujuan = $row_perjanjian['NAMAPOLI'];
																	$idDokter = $row_perjanjian['ID_DOKTER'];
																	$smf = $row_perjanjian['SMF'];
																	$waktumulai = $row_perjanjian['LOCK'];
																	$awal = $row_perjanjian['AWAL'];
																	$akhir = $row_perjanjian['AKHIR'];
																	$rencana = $row_perjanjian['RENCANA'];
																	if($row_perjanjian['ID_RUANGAN'] == "105020901"){
																		$poliTujuan = "HDL";
																		$namaPoliTujuan = "Hemodialisa";
																	}
																	$hafis = $hafisSekarang;
															?>
																	<div class="ferry ferry-from">
																		<label>Nama pasien :</label>
																		<input type="text" name="pasien" id="pasien" readonly="readonly" value="<?php echo $row_perjanjian['NAMAPASIEN']; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																	</div>
																	<div class="ferry ferry-from">
																		<label>Nomor mr :</label>
																		<input type="text" name="nomr" id="nomr" readonly="readonly" value="<?php echo $mr; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																	</div>
																	<div class="ferry ferry-from">
																		<label>Nomor kartu BPJS : </label>
																		<input type="text" name="nobpjs" id="nobpjs" readonly="readonly" value="<?php echo $kartubpjs; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																	</div>
																	<div class="ferry ferry-from">
																		<label>Nomor Rujukan</label>
																		<?php
																		//cek pendaftaran simpel terakhir
																		$sql_poli="SELECT mr.JENIS_KUNJUNGAN, tp.RUANGAN FROM pendaftaran.pendaftaran pp
																			LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = pp.NOMOR
																			LEFT JOIN master.ruangan mr ON mr.ID = tp.RUANGAN AND mr.JENIS=5
																			WHERE pp.NORM = '$mr' ORDER BY pp.TANGGAL DESC LIMIT 1";

																		$query_poli = mysqli_query($conn,$sql_poli);
																		if($query_poli){
																			$row_poli = mysqli_fetch_assoc($query_poli);
																			$jenisPoliTujuan = $row_poli['JENIS_KUNJUNGAN'];
																			// SIMPEL 3: Ranap
																			if($jenisPoliTujuan != 3){ ?>
																				<input type="text" name="rujukan" id="rujukan" readonly="readonly" value="<?php echo $noRujukan; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;" />
																			<?php
																			}else{
																				$sql_layanan = "SELECT bpkj.jenisPelayanan, bpkj.noSEP noSEPRanap, bpkj.dpjp 
																						FROM bpjs.peserta bps 
																						LEFT JOIN bpjs.kunjungan bpkj ON bps.noKartu=bpkj.noKartu

																						WHERE bps.norm='$mr' AND bpkj.errMsgBatalSEP IS NULL 
																						ORDER BY bpkj.tglSEP DESC LIMIT 1";
																				$query_layanan = mysqli_query($conn, $sql_layanan);
																				$row_layanan = mysqli_fetch_assoc($query_layanan);

																				if($query_layanan){
																					$hafisSebelum = $row_layanan['dpjp'];
																					$jenisRuang = $row_layanan['jenisPelayanan'];
																					#BPJS 1: Ranap, 2: Rajal
																					if($jenisRuang == 1){ $hafis = $hafisSekarang; ?>
																						<input type="text" name="rujukan" id="rujukan" readonly="readonly" value="<?php echo $noRujukan; ?>" style="text-align: left; height: 60px; font-size: 150%; font-weight: bold;" />
																					<?php
																					}else{
																						$hafis = $hafisSebelum;
																					?>
																						<input type="text" name="rujukan" id="rujukan" readonly="readonly" value="<?php echo $noRujukan; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;" />
																						<input type="text" name="idhafis" id="idhafis" value="<?php echo $hafis; ?>" hidden=""/>
																				<?php
																					}
																				}else{
																					echo $sql_layanan;
																				}
																			}
																		}
									?>
																		<input type="text" name="idhafis" id="idhafis" value="<?php echo $hafis; ?>" hidden=""/>

																		<input type="text" name="nokontrol" id="nokontrol" readonly="readonly" value="<?php echo $nokontrol; ?>" hidden="" />

																		<input type="text" name="diagawal" id="diagawal" readonly="readonly" value="<?php echo $diagawal; ?>" hidden="" />

																		<input type="text" name="ppkrujukan" id="ppkrujukan" readonly="readonly" value="<?php echo $ppkRujukan; ?>" hidden="" />

																		<input type="text" name="notelp" id="notelp" readonly="readonly" value="<?php echo $noTelp; ?>" hidden="" />

																		<?php
																			$noSEP = "";
																			$statusKunjungan=0;
																			$sql_poli = "SELECT p.NOMOR,p.TANGGAL,p.RUJUKAN,p.`STATUS` STATUS_PENDAFTRAN,r.DESKRIPSI, r.JENIS_KUNJUNGAN FROM pendaftaran.pendaftaran p 
																			LEFT JOIN pendaftaran.tujuan_pasien t ON p.NOMOR=t.NOPEN
																			LEFT JOIN `master`.ruangan r ON t.RUANGAN=r.ID
																			WHERE p.NORM = '$mr' 
																			AND p.RUJUKAN NOT IN('0','0904R008') AND p.`STATUS` != 0 AND t.`STATUS` != 0 AND r.JENIS_KUNJUNGAN NOT IN (2,3,4,11) ORDER BY p.TANGGAL DESC LIMIT 1";
																			
																			$query_poli = mysqli_query($conn, $sql_poli);
										
																			if($query_poli){
																				$row_poli = mysqli_fetch_assoc($query_poli);

																				$noRujukanSimpel = $row_poli['RUJUKAN'];
																				//echo $poliTujuan;
																				if(isset($noRujukanSimpel) || $noRujukanSimpel != ''){
																					if($noRujukan == $noRujukanSimpel){
																						//echo $poliTujuan;
																						$poliTujuan = $poliTujuan;
																					}else{
																						$poliTujuan = $poliRujukan;
																						$statusKunjungan=1;
																					}
																				}
																			}
																		?>
																		<input type="text" name="poliRujukan" id="poliRujukan" readonly="readonly" value="<?php echo $poliRujukan; ?>" hidden="" />

																		<input type="text" name="poliTujuan" id="poliTujuan" readonly="readonly" value="<?php echo $poliTujuan; ?>" hidden="" />

																		<input type="text" name="namaPoliTujuan" id="namaPoliTujuan" readonly="readonly" value="<?php echo $namaPoliTujuan; ?>" hidden=""/>

																		<input type="text" name="tglRujukan" id="tglRujukan" readonly="readonly" value="<?php echo $tglRujukan ?>" hidden="" />
																		<?php
																			if($hakKelas == "KELAS III"){
																				$idHakKelas = 3;
																			}elseif($hakKelas == "KELAS II"){
																				$idHakKelas = 2;
																			}elseif($hakKelas == "KELAS I"){
																				$idHakKelas = 1;
																			}
																		?>
																		<input type="text" name="kelasRawat" id="kelasRawat" readonly="readonly" value="<?php echo $idHakKelas; ?>" hidden="" />

																		<input type="text" name="idDokter" id="idDokter" readonly="readonly" value="<?php echo $idDokter; ?>" hidden="" />

																		<input type="text" name="smf" id="smf" readonly="readonly" value="<?php echo $smf; ?>" hidden="" />

																		<input type="text" name="tglRencanaKontrol" id="tglRencanaKontrol" readonly="readonly" value="<?php echo $row_perjanjian['TGLPERJANJIAN']; ?>" hidden="" />

																		<input type="text" name="statusKunjungan" readonly="readonly" value="<?php echo $statusKunjungan; ?>" hidden="" />
																	</div>
																	<div class="ferry ferry-from">
																		<label>Poli Tujuan :</label>
																		<input type="text" name="ruangan" id="ruangan" readonly="readonly" value="<?php echo $row_perjanjian['RUANGAN']; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																		<input type="text" name="idruangan" id="idruangan" readonly="readonly" value="<?php echo $row_perjanjian['ID_RUANGAN']; ?>" hidden="" />
																	</div>
																	<div class="ferry ferry-from">
																		<label>Dokter  :</label>
																		<input type="text" name="namaDokter" id="namaDokter" readonly="readonly" value="<?php echo $row_perjanjian['NAMADOKTER']; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																		<input type="hidden" name="hafisSK" id="hafisSK" readonly="readonly" value="<?php echo $hafisSekarang; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																	</div>
															<?php
															}
															}else{
																//tdk ada perjanjian
															?>
																<script type='text/javascript'>
																	var status = "Mohon Maaf Anda belum melakukan perjanjian. Silakan mengambil antrian.";
																	document.addEventListener("DOMContentLoaded", function(event) {
																	swal({
																			icon : 'warning',
																			title : 'Info!',
																			text: status,
																			timer: 5000,
																			button : false
																		}).then(function() {
																		window.location = "index.php";
																		});
																	});
																</script>
															<?php			
															}//end perjanjian
															?>
																<br><br>
																<div class="wthreesubmitaits">
																	<?php
																	// echo $poliTujuan.'-'.$waktumulai;
																		if($waktumulai == 1) {
																	?>
																		<div id="daftar" style="display:none;">
																			<input type="submit" name="submit" id="daftarSub" value="Daftar" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;">
																		</div>
																		<div id="scan" style="display:none;">
																			<a href="#" class="btn btn-success btn-md biometrik" id="fp">Buka Aplikasi Sidik Jari</a>
																			<!-- <a href="#" class="btn btn-info btn-md biometrik" id="fr">Buka Aplikasi Scan Wajah</a> -->
																			<p class="text-danger" style="text-align: center; font-size: 150%; font-weight: bold; background-color: #d4edda; border-radius: 10px; padding: 10px;">Silahkan lakukan validasi biometrik terlebih dahulu</p>
																		</div>
																	<?php
																		} else {
																	?>
																			<p class="btn-warning" style="text-align: center; height: 60px; font-size: 100%; font-weight: bold;">Waktu daftar baru dapat dilakukan 1 jam sebelum jadwal dokter dimulai sampai 30 menit sebelum jadwal dokter selesai.<br/> Jadwal Dokter: <?= $awal .'-'.$akhir ?>  </p>
																	<?php
																		}
																	?>
																<script>
																	function goBack() {
																		window.history.back();
																	}
																</script>
															</div>
														</form> <!-- end form -->
													</div> <!-- end wrapper -->
												</div> <!-- end w3layoutscontactagileits -->
											</div> <!-- end containerw3layouts-agileits -->
										</div><!-- end col-md-8 -->
									</div><!-- end row -->
								<?php
								}//end query perjanjian
							}else{
								//pasien tdk aktif
								?>
								<script type='text/javascript'>
									var status = "<?php echo $statusPeserta; ?>";
									document.addEventListener("DOMContentLoaded", function(event) {
									swal({
											icon : 'warning',
											title : 'Info!',
											text: "Status pasien "+ status,
											timer: 5000,
											button : false
										}).then(function() {
										window.location = "index.php";
										});
									});
								</script>
							<?php
							}
						}else{
							$kartubpjs = $row_kartu['kartubpjs'];
							$noRujukan = $row_kartu['rujukan'];
							//cek perjanjian
							$sql_perjanjian = "SELECT NOMR, NAMAPASIEN, ID_DOKTER
									,master.getNamalengkapPegawai(mdok.NIP) NAMADOKTER
									, mdok.HAFIS, rmp.TANGGAL TGLPERJANJIAN, NOKONTROL
									, ID_RUANGAN,  mr.DESKRIPSI RUANGAN, mpeg.SMF
									, pr.RUANGAN_PENJAMIN, pb.nmsubspesialis NAMAPOLI
									#, (SELECT RUANGAN_PENJAMIN FROM master.penjamin_ruangan WHERE RUANGAN_RS = mpeg.SMF AND STATUS=1) RUANGAN_PENJAMIN
									, IF(STR_TO_DATE(rmj.AWAL - 1, '%H:%i:%s') < CURRENT_TIME() 
									, IF(STR_TO_DATE(rmj.AKHIR , '%H:%i:%s') - INTERVAL '30' MINUTE  > CURRENT_TIME(),1,1)
									, 2) WAKTU_MULAI
									, rmj.AWAL
									, rmj.AKHIR
									, rmp.RENCANA
									, IF(STR_TO_DATE(rmj.AWAL - 1, '%H:%i:%s') < CURRENT_TIME() 
										AND STR_TO_DATE(rmj.AKHIR , '%H:%i:%s') - INTERVAL '30' MINUTE > CURRENT_TIME()
									,1,1) `LOCK` #aktif lock 1,0
								FROM remun_medis.perjanjian rmp 
								LEFT JOIN master.dokter mdok ON mdok.ID = rmp.ID_DOKTER
								LEFT JOIN master.pegawai mpeg ON mpeg.NIP = mdok.NIP AND mdok.`STATUS` != 0
								LEFT JOIN `master`.penjamin_ruangan pr ON mpeg.SMF = pr.RUANGAN_RS
								LEFT JOIN `master`.poli_bpjs_2 pb ON pr.RUANGAN_PENJAMIN = pb.kdsubspesialis
								LEFT JOIN remun_medis.jadwal rmj ON rmj.DOKTER = rmp.ID_DOKTER AND rmj.RUANGAN = rmp.ID_RUANGAN AND rmj.TANGGAL = rmp.TANGGAL AND rmj.`STATUS` != 0
								LEFT JOIN remun_medis.unlock_cetak_perjanjian unr ON unr.REF = rmp.ID_RUANGAN AND unr.JENIS=1
								LEFT JOIN remun_medis.unlock_cetak_perjanjian und ON und.REF = rmp.ID_DOKTER AND und.JENIS=2 
                				LEFT JOIN `master`.ruangan mr ON rmp.ID_RUANGAN = mr.ID
								WHERE rmp.TANGGAL = DATE(NOW()) AND NOMR = $mr AND rmp.STATUS != 0
								AND rmp.ID_RUANGAN NOT IN ('105020201')
								#AND rmp.ID_RUANGAN IN ('105020704','105020705','105120101','105060101','105110101', '105020401')
								ORDER BY NOKONTROL ASC LIMIT 1";
								// echo die($sql_perjanjian);
							$query_perjanjian = mysqli_query($conn, $sql_perjanjian);
							if($query_perjanjian){
							?>
								<h1 class="text-danger">KIOS TANPA SEP</h1>
								<h1><span style="background:hsla(0, 0.00%, 100.00%, 0.5);font-size:80px;padding:16px;color:#267b84;font-weight: bold;">KIOSK |<img src="images/logo.png" style="width: 18%;"></span></h1><br/>
								<div class="row">
									<div class="col-md-2" align="right"><input type="button" class="btn btn-success" value="Back" onclick="goBack()" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;"></div>
									<div class="col-md-8" align="center">
										<div class="containerw3layouts-agileits" style="width: 600px;">
											<div class="w3layoutscontactagileits">	
												<div id="wrapper">
													<form name="calc" action="dataSep.php" method="post" id="calc">
														<input type="text" name="sep" readonly="readonly" value="0" hidden/>
														<?php
														$rowcount=mysqli_num_rows($query_perjanjian);
														if($rowcount > 0){
															while($row_perjanjian = mysqli_fetch_assoc($query_perjanjian)){
																$hafisSekarang = $row_perjanjian['HAFIS'];
																$nokontrol = $row_perjanjian['NOKONTROL'];
																$poliTujuan = $row_perjanjian['RUANGAN_PENJAMIN'];
																$namaPoliTujuan = $row_perjanjian['NAMAPOLI'];
																$idDokter = $row_perjanjian['ID_DOKTER'];
																$smf = $row_perjanjian['SMF'];
																$waktumulai = $row_perjanjian['LOCK'];
																$awal = $row_perjanjian['AWAL'];
																$akhir = $row_perjanjian['AKHIR'];
																$rencana = $row_perjanjian['RENCANA'];
																if($row_perjanjian['ID_RUANGAN'] == "105020901"){
																	$poliTujuan = "HDL";
																	$namaPoliTujuan = "Hemodialisa";
																}
																$hafis = $hafisSekarang;
														?>
																<div class="ferry ferry-from">
																	<label>Nama pasien :</label>
																	<input type="text" name="pasien" id="pasien" readonly="readonly" value="<?php echo $row_perjanjian['NAMAPASIEN']; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																</div>
																<div class="ferry ferry-from">
																	<label>Nomor mr :</label>
																	<input type="text" name="nomr" id="nomr" readonly="readonly" value="<?php echo $mr; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																</div>
																<div class="ferry ferry-from">
																	<label>Nomor kartu BPJS : </label>
																	<input type="text" name="nobpjs" id="nobpjs" readonly="readonly" value="<?php echo $kartubpjs; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																</div>
																<div class="ferry ferry-from">
																	<label>Nomor Rujukan</label>
																	<?php
																	//cek pendaftaran simpel terakhir
																	$sql_poli="SELECT mr.JENIS_KUNJUNGAN, tp.RUANGAN FROM pendaftaran.pendaftaran pp
																		LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = pp.NOMOR
																		LEFT JOIN master.ruangan mr ON mr.ID = tp.RUANGAN AND mr.JENIS=5
																		WHERE pp.NORM = '$mr' ORDER BY pp.TANGGAL DESC LIMIT 1";

																	$query_poli = mysqli_query($conn,$sql_poli);
																	if($query_poli){
																		$row_poli = mysqli_fetch_assoc($query_poli);
																		$jenisPoliTujuan = $row_poli['JENIS_KUNJUNGAN'];
																		// SIMPEL 3: Ranap
																		if($jenisPoliTujuan != 3){ ?>
																			<input type="text" name="rujukan" id="rujukan" readonly="readonly" value="<?php echo $noRujukan; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;" />
																		<?php
																		}else{
																			$sql_layanan = "SELECT bpkj.jenisPelayanan, bpkj.noSEP noSEPRanap, bpkj.dpjp 
																					FROM bpjs.peserta bps 
																					LEFT JOIN bpjs.kunjungan bpkj ON bps.noKartu=bpkj.noKartu

																					WHERE bps.norm='$mr' AND bpkj.errMsgBatalSEP IS NULL 
																					ORDER BY bpkj.tglSEP DESC LIMIT 1";
																			$query_layanan = mysqli_query($conn, $sql_layanan);
																			$row_layanan = mysqli_fetch_assoc($query_layanan);

																			if($query_layanan){
																				$hafisSebelum = $row_layanan['dpjp'];
																				$jenisRuang = $row_layanan['jenisPelayanan'];
                                        										#BPJS 1: Ranap, 2: Rajal
																				if($jenisRuang == 1){ $hafis = $hafisSekarang; ?>
																					<input type="text" name="rujukan" id="rujukan" readonly="readonly" value="<?php echo $noRujukan; ?>" style="text-align: left; height: 60px; font-size: 150%; font-weight: bold;" />
																				<?php
																				}else{
																					$hafis = $hafisSebelum;
																				?>
																					<input type="text" name="rujukan" id="rujukan" readonly="readonly" value="<?php echo $noRujukan; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;" />
																					<input type="text" name="idhafis" id="idhafis" value="<?php echo $hafis; ?>" hidden=""/>
																			<?php
																				}
																			}else{
																				echo $sql_layanan;
																			}
																		}
																	}
                                  ?>
																	<input type="text" name="idhafis" id="idhafis" value="<?php echo $hafis; ?>" hidden=""/>

																	<input type="text" name="nokontrol" id="nokontrol" readonly="readonly" value="<?php echo $nokontrol; ?>" hidden="" />

																	<input type="text" name="diagawal" id="diagawal" readonly="readonly" value="<?php echo $diagawal; ?>" hidden="" />

																	<input type="text" name="ppkrujukan" id="ppkrujukan" readonly="readonly" value="<?php echo $ppkRujukan; ?>" hidden="" />

																	<input type="text" name="notelp" id="notelp" readonly="readonly" value="<?php echo $noTelp; ?>" hidden="" />

																	<?php
																		$noSEP = "";
																		$statusKunjungan=0;
																		$sql_poli = "SELECT p.NOMOR,p.TANGGAL,p.RUJUKAN,p.`STATUS` STATUS_PENDAFTRAN,r.DESKRIPSI, r.JENIS_KUNJUNGAN FROM pendaftaran.pendaftaran p 
																		LEFT JOIN pendaftaran.tujuan_pasien t ON p.NOMOR=t.NOPEN
																		LEFT JOIN `master`.ruangan r ON t.RUANGAN=r.ID
																		WHERE p.NORM = '$mr' 
																		AND p.RUJUKAN NOT IN('0','0904R008') AND p.`STATUS` != 0 AND t.`STATUS` != 0 AND r.JENIS_KUNJUNGAN NOT IN (2,3,4,11) ORDER BY p.TANGGAL DESC LIMIT 1";
																		
																		$query_poli = mysqli_query($conn, $sql_poli);
                                    
																		if($query_poli){
																			$row_poli = mysqli_fetch_assoc($query_poli);

																			$noRujukanSimpel = $row_poli['RUJUKAN'];
																			//echo $poliTujuan;
																			if(isset($noRujukanSimpel) || $noRujukanSimpel != ''){
																				if($noRujukan == $noRujukanSimpel){
																					//echo $poliTujuan;
																					$poliTujuan = $poliTujuan;
																				}else{
																					$poliTujuan = $poliRujukan;
																					$statusKunjungan=1;
																				}
																			}
																		}
																	?>
																	<input type="text" name="poliRujukan" id="poliRujukan" readonly="readonly" value="<?php echo $poliRujukan; ?>" hidden="" />

																	<input type="text" name="poliTujuan" id="poliTujuan" readonly="readonly" value="<?php echo $poliTujuan; ?>" hidden="" />

																	<input type="text" name="namaPoliTujuan" id="namaPoliTujuan" readonly="readonly" value="<?php echo $namaPoliTujuan; ?>" hidden=""/>

																	<input type="text" name="tglRujukan" id="tglRujukan" readonly="readonly" value="<?php echo $tglRujukan ?>" hidden="" />
																	<?php
																		if($hakKelas == "KELAS III"){
																			$idHakKelas = 3;
																		}elseif($hakKelas == "KELAS II"){
																			$idHakKelas = 2;
																		}elseif($hakKelas == "KELAS I"){
																			$idHakKelas = 1;
																		}
																	?>
																	<input type="text" name="kelasRawat" id="kelasRawat" readonly="readonly" value="<?php echo $idHakKelas; ?>" hidden="" />

																	<input type="text" name="idDokter" id="idDokter" readonly="readonly" value="<?php echo $idDokter; ?>" hidden="" />

																	<input type="text" name="smf" id="smf" readonly="readonly" value="<?php echo $smf; ?>" hidden="" />

																	<input type="text" name="tglRencanaKontrol" id="tglRencanaKontrol" readonly="readonly" value="<?php echo $row_perjanjian['TGLPERJANJIAN']; ?>" hidden="" />

																	<input type="text" name="statusKunjungan" readonly="readonly" value="<?php echo $statusKunjungan; ?>" hidden="" />
																</div>
																<div class="ferry ferry-from">
																	<label>Poli Tujuan :</label>
																	<input type="text" name="ruangan" id="ruangan" readonly="readonly" value="<?php echo $row_perjanjian['RUANGAN']; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																	<input type="text" name="idruangan" id="idruangan" readonly="readonly" value="<?php echo $row_perjanjian['ID_RUANGAN']; ?>" hidden="" />
																</div>
																<div class="ferry ferry-from">
																	<label>Dokter  :</label>
																	<input type="text" name="namaDokter" id="namaDokter" readonly="readonly" value="<?php echo $row_perjanjian['NAMADOKTER']; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																	<input type="hidden" name="hafisSK" id="hafisSK" readonly="readonly" value="<?php echo $hafisSekarang; ?>" style="text-align: center; height: 60px; font-size: 150%; font-weight: bold;">
																</div>
														<?php
														  }
														}else{
															//tdk ada perjanjian
														?>
															<script type='text/javascript'>
																var status = "Mohon Maaf Anda belum melakukan perjanjian. Silakan mengambil antrian.";
															    document.addEventListener("DOMContentLoaded", function(event) {
															    swal({
																		icon : 'warning',
																		title : 'Info!',
																		text: status,
																		timer: 5000,
																		button : false
																	}).then(function() {
																    window.location = "index.php";
																	});
															    });
															</script>
                            							<?php			
														}//end perjanjian
														?>
															<br><br>
															<div class="wthreesubmitaits">
																<?php
																// echo $poliTujuan.'-'.$waktumulai;
																	if($waktumulai == 1) {
																?>
																	<input type="submit" name="submit" id="daftarSub" value="Daftar" style="text-align: center; height: 60px; font-size: 200%; font-weight: bold;">
																	<?php if(!in_array($row_rencana['RENCANA'],$rencanaTanpaSep)){ ?>
																<?php
																		}
																	} else {
																?>
																		<p class="btn-warning" style="text-align: center; height: 60px; font-size: 100%; font-weight: bold;">Waktu daftar baru dapat dilakukan 1 jam sebelum jadwal dokter dimulai sampai 30 menit sebelum jadwal dokter selesai.<br/> Jadwal Dokter: <?= $awal .'-'.$akhir ?>  </p>
																<?php
																	}
																?>
															<script>
																function goBack() {
																	window.history.back();
																}
															</script>
														</div>
													</form> <!-- end form -->
												</div> <!-- end wrapper -->
											</div> <!-- end w3layoutscontactagileits -->
										</div> <!-- end containerw3layouts-agileits -->
									</div><!-- end col-md-8 -->
								</div><!-- end row -->
							<?php
							}//end query perjanjian
						}
					}
				} //end tagihan
			} //end bukan pasien bpjs
		} //query kartu
	} // post key	
	}
}
?>
<div class="col-md-6" align="center"></div>

<div class="w3lsfooteragileits">
	<p style="color: #008000;"> &copy; SIMRS 2018 Aplikasi Pendaftaran Mandiri. All Rights Reserved </p>
</div>

</body>

<!-- Necessary-JavaScript-Files-&-Links -->
<!-- Date-Picker-JavaScript -->
<script type="text/javascript" src="js/jquery-2.1.4.min.js"></script>
<script  src="js/jquery-ui.js"></script>	
<script  src="js/wickedpicker.js"></script>	
<script src="js/bootstrap.min.js"></script>
<script src="datatables/jquery.dataTables.js"></script>
<script src="datatables/dataTables.bootstrap.js"></script>

<script type="text/javascript">
	//paste this code under the head tag or in a separate js file.
	// Wait for window load
	$(window).load(function() {
		// Animate loader off screen
		$(".se-pre-con").fadeOut("slow");;
	});
</script>

<script>
  $(document).ready(function () {
	async function checkFingerprintStatus(aplikasi='fp') {
		var noKartu = $("#nobpjs").val();
		var tanggal = $("#tglRencanaKontrol").val();
		var polinofp = ['ANA','030'];
		var poli = $("#poliTujuan").val();
		var error = "<?= $error?>";
		if(polinofp.includes(poli) || error == 1){
			$("#daftar").show();
			$("#scan").hide();
		}else{
			var isFingerprintValid = await validateFingerprint(noKartu, tanggal);
			if(isFingerprintValid) {
				$("#daftar").show();
				$("#scan").hide();
				closeApp();
			}else {	
				$("#daftar").hide();
				$("#scan").show();
				copyToClipboard(aplikasi);
			}

			var intervalId = setInterval(async function() {
				var isFingerprintValid = await validateFingerprint(noKartu, tanggal);
				if(isFingerprintValid) {
					$("#daftar").show();
					$("#scan").hide();
					clearInterval(intervalId);
				}else {	
					$("#daftar").hide();
					$("#scan").show();
				}
			}, 20000);
		}
	}

	function validateFingerprint(noKartu, tanggal) {
		return new Promise((resolve, reject) => {
			$.ajax({
				url: 'http://*************/apijkn/vclaimv2/sep/fingerprint/peserta',
				type: 'GET',
				data: { noKartu: noKartu, tglPelayanan: tanggal },
				success: function (response) {
					if (response.response.kode != 1) {
						resolve(false);
					} else {
						resolve(true);
					}
				},
				error: function (xhr, status, error) {
					resolve(false);
				}
			});
		});
	}


	async function copyToClipboard(aplikasi) {
		var nokartu = "<?php echo $kartubpjs; ?>";

		try {
			await navigator.clipboard.writeText(nokartu);
			console.log('Text copied to clipboard');
		} catch (err) {
			console.error('Unable to copy text to clipboard', err);
		}

		var xhr = new XMLHttpRequest();
		if(aplikasi == 'fr'){
			xhr.open("GET", "http://localhost:3000/openFR", true);
		}else{
			xhr.open("GET", "http://localhost:3000/runBatch", true);
		}
		xhr.onreadystatechange = function() {};
		xhr.send();
	}

	async function closeApp() {
		var xhr = new XMLHttpRequest();

		xhr.open("GET", "http://localhost:3000/closeApp", true);
		xhr.onreadystatechange = function() {};
		xhr.send();
	}

	$(document).on('submit', '#calc',function (e) {
      	$("#daftarSub").attr("disabled", true);
      	return true;
    });
	
	$(document).on('click','.biometrik', function (e) {
		e.preventDefault();
		var aplikasi = $(this).attr('id');
		checkFingerprintStatus(aplikasi);
	})
  });
</script>

</html>
