.wickedpicker {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	box-shadow: 0 0 0 1px rgba(14, 41, 57, 0.12), 0 2px 5px rgba(14, 41, 57, 0.44), inset 0 -1px 2px rgba(14, 41, 57, 0.15);
	background: #fefefe;
	margin: 0 auto;
	border-radius: 0.1px;
	width: 318px;
	height: 130px;
	font-size: 14px;
	display: none; }
.wickedpicker__title {
    background-image: -webkit-linear-gradient(top, #ffffff 0%, #f2f2f2 100%);
    background-image: linear-gradient(to bottom, #ffffff 0%, #f2f2f2 100%);
    position: relative;
    background: #f2f2f2;
    margin: 0 auto;
    border-bottom: 1px solid #e5e5e5;
    padding: 12px 11px 10px 15px;
    color: #4C4C4C;
    font-size: inherit; }
.wickedpicker__close {
    -webkit-transform: translateY(-25%);
    -moz-transform: translateY(-25%);
    -ms-transform: translateY(-25%);
    -o-transform: translateY(-25%);
    transform: translateY(-25%);
    position: absolute;
    top: 25%;
    right: 5px;
    color: #34495e;
    cursor: pointer; }
.wickedpicker__close:before {
    content: '\e802'; 
}
.wickedpicker__controls {
    padding: 10px 0;
    line-height: normal;
    margin: 0; 
}
.wickedpicker__controls__control, .wickedpicker__controls__control--separator {
    vertical-align: middle;
    display: inline-block;
    font-size: inherit;
    margin: 0 auto;
    width: 35px;
    letter-spacing: 1.3px; 
}
.wickedpicker__controls__control-up, .wickedpicker__controls__control-down {
    color: #34495e;
    position: relative;
    display: block;
    margin: 3px auto;
    font-size: 18px;
    cursor: pointer; 
}
.wickedpicker__controls__control-up:before {
    content: '\e800'; 
}
.wickedpicker__controls__control-down:after {
    content: '\e801'; 
}
.wickedpicker__controls__control--separator {
	width: 5px; 
}
.text-center, .wickedpicker__title, .wickedpicker__controls, .wickedpicker__controls__control, .wickedpicker__controls__control--separator, .wickedpicker__controls__control-up, .wickedpicker__controls__control-down {
	text-align: center; 
}
.hover-state {
	color: #3498db; 
}
@font-face {
	font-family: 'fontello';
	src: url("../fonts/fontello.eot?52602240");
	src: url("../fonts/fontello.eot?52602240#iefix") format("embedded-opentype"), url("../fonts/fontello.woff?52602240") format("woff"), url("../fonts/fontello.ttf?52602240") format("truetype"), url("../fonts/fontello.svg?52602240#fontello") format("svg");
	font-weight: normal;
	font-style: normal; 
}
.fontello:before, .wickedpicker__close:before, .wickedpicker__controls__control-up:before, .fontello-after:after, .wickedpicker__controls__control-down:after {
	font-family: 'fontello';
	font-style: normal;
	font-weight: normal;
	speak: none;
	display: inline-block;
	text-decoration: inherit;
	width: 1em;
	margin-right: .2em;
	text-align: center;
	font-variant: normal;
	text-transform: none;
	line-height: 1em;
	margin-left: .2em;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale; 
}

span {
    color: #05b1ff;
}

@media(max-width:991px){
	.wickedpicker {
		width: 309px;
	}
}
@media(max-width:900px){
	.wickedpicker {
		width: 274px;
	}
}

@media(max-width:768px){
	.wickedpicker {
		width: 270px;
	}
}
@media(max-width:736px){
	.wickedpicker {
		width: 256px;
	}
}
@media(max-width:667px){
	.wickedpicker {
		width: 223px;
		    height: 115px;
	}
	.wickedpicker__title {
		padding: 8px 11px 7px 15px;
	}
	.wickedpicker__controls {
		padding: 3px 0;
	}
}
@media(max-width:640px){
	.wickedpicker {
		width: 215px;
	}
}
@media(max-width:480px){
	.wickedpicker {
		width: 176px;
	}
}
@media(max-width:414px){
	.wickedpicker {
		width: 295px;
	}
}
@media(max-width:384px){
	.wickedpicker {
		width: 271px;
	}
}
@media(max-width:375px){
	.wickedpicker {
		width: 264px;
	}
}
@media(max-width:320px){
	.wickedpicker {
		width: 224px;
	}
}