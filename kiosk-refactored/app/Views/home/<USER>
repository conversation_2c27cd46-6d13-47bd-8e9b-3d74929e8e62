<?php
$content = ob_start();
?>

<div class="text-center">
    <h2 class="mb-4" style="color: #267b84; font-weight: bold;">Masukkan No MR Anda</h2>
    
    <form name="calc" action="/home/<USER>" method="post" autocomplete="off" id="mrForm">
        <input type="hidden" name="key" value="crtghb68p2xb6jc44gcdx9jhm">
        
        <!-- Input Nomor MR -->
        <div class="form-group mb-4">
            <input 
                id="nomr" 
                name="nomr" 
                type="text" 
                class="form-control mr-input" 
                placeholder="Nomor MR"
                maxlength="10"
                required
                autofocus
                readonly
            >
        </div>

        <!-- Virtual Keypad -->
        <div class="row justify-content-center">
            <div class="col-md-8">
                <table class="table table-borderless">
                    <tr>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('1')">1</button></td>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('2')">2</button></td>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('3')">3</button></td>
                    </tr>
                    <tr>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('4')">4</button></td>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('5')">5</button></td>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('6')">6</button></td>
                    </tr>
                    <tr>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('7')">7</button></td>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('8')">8</button></td>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('9')">9</button></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td><button type="button" class="keypad-btn" onclick="addNumber('0')">0</button></td>
                        <td>
                            <button type="button" class="keypad-btn" onclick="backspace()" style="background-image: url(/assets/images/backspace.png); background-size: 50% 50%; background-repeat: no-repeat; background-position: center;">
                            </button>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="mt-4">
            <button type="submit" class="btn btn-success btn-kiosk" id="submitBtn" disabled>
                <i class="fa fa-search"></i> Cari
            </button>
            <button type="button" class="btn btn-warning btn-kiosk ml-3" onclick="clearInput()">
                <i class="fa fa-refresh"></i> Clear
            </button>
        </div>
    </form>

    <!-- Help Button -->
    <div class="mt-4">
        <a href="/home/<USER>" class="btn btn-info btn-sm">
            <i class="fa fa-question-circle"></i> Bantuan
        </a>
    </div>
</div>

<?php
$content = ob_get_clean();

$additionalJs = '
<script>
    let currentInput = "";
    const maxLength = 10;

    function addNumber(num) {
        if (currentInput.length < maxLength) {
            currentInput += num;
            updateDisplay();
            validateInput();
        }
    }

    function backspace() {
        currentInput = currentInput.slice(0, -1);
        updateDisplay();
        validateInput();
    }

    function clearInput() {
        currentInput = "";
        updateDisplay();
        validateInput();
    }

    function updateDisplay() {
        document.getElementById("nomr").value = currentInput;
    }

    function validateInput() {
        const submitBtn = document.getElementById("submitBtn");
        if (currentInput.length > 0) {
            submitBtn.disabled = false;
            submitBtn.classList.remove("btn-secondary");
            submitBtn.classList.add("btn-success");
        } else {
            submitBtn.disabled = true;
            submitBtn.classList.remove("btn-success");
            submitBtn.classList.add("btn-secondary");
        }
    }

    // Prevent manual input
    document.getElementById("nomr").addEventListener("keydown", function(e) {
        e.preventDefault();
    });

    // Form validation
    document.getElementById("mrForm").addEventListener("submit", function(e) {
        if (currentInput.length === 0) {
            e.preventDefault();
            showAlert("error", "Error", "Silakan masukkan nomor MR");
            return false;
        }

        if (!/^[0-9]+$/.test(currentInput)) {
            e.preventDefault();
            showAlert("error", "Error", "Nomor MR hanya boleh berisi angka");
            return false;
        }

        showLoading();
        return true;
    });

    // Auto-focus on load
    document.addEventListener("DOMContentLoaded", function() {
        validateInput();
    });

    // Keyboard support
    document.addEventListener("keydown", function(e) {
        if (e.key >= "0" && e.key <= "9") {
            addNumber(e.key);
        } else if (e.key === "Backspace") {
            backspace();
        } else if (e.key === "Enter") {
            if (!document.getElementById("submitBtn").disabled) {
                document.getElementById("mrForm").submit();
            }
        } else if (e.key === "Escape") {
            clearInput();
        }
    });
</script>
';

// Include layout
include __DIR__ . '/../layouts/main.php';
?>
