# Kiosk Pendaftaran Mandiri RSKD - Refactored

Aplikasi kiosk untuk pendaftaran mandiri rumah sakit dengan integrasi BPJS yang telah direfactor menggunakan best practice.

## Struktur Proyek

```
kiosk-refactored/
├── app/
│   ├── Controllers/     # Controller classes untuk MVC pattern
│   ├── Models/         # Model classes untuk data handling
│   ├── Services/       # Service classes untuk business logic
│   └── Views/          # Template files untuk UI
├── config/             # File konfigurasi aplikasi
├── public/             # Public assets dan entry point
│   ├── assets/         # CSS, JS, images
│   └── index.php       # Entry point aplikasi
├── vendor/             # Composer dependencies
├── logs/               # Log files
├── .env                # Environment variables
└── composer.json       # Composer configuration
```

## Fitur Utama

- Input nomor MR pasien dengan keypad virtual
- Validasi pasien BPJS
- Cek rujukan dan kepesertaan BPJS
- Generate SEP (Surat Eligibilitas Peserta)
- Print bukti registrasi
- Integrasi dengan web service BPJS

## Teknologi

- PHP 7.4+
- MySQL/MariaDB
- Bootstrap 3
- jQuery
- Guzzle HTTP Client

## Instalasi

1. Copy file `.env.example` ke `.env` dan sesuaikan konfigurasi
2. Jalankan `composer install`
3. Pastikan web server mengarah ke folder `public/`

## Best Practice yang Diterapkan

- MVC Pattern
- PSR-4 Autoloading
- Environment-based Configuration
- Separation of Concerns
- Security Improvements
- Proper Asset Organization
