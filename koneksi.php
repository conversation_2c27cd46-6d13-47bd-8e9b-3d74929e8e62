<?php
// definisikan koneksi ke database

$config = parse_ini_file("config.cnf", true);
$env = $config["ENV"]["env"];
$error = $config["ENV"]["error"];

$dev = $config["DEV"];
$pro = $config["PRO"];
if($env == 'pro'){
  $server = $pro["server"];
  $username = $pro["username"];
  $password = $pro["password"];
  $database = $pro["database"];
}else{
  $server = $dev["server"];
  $username = $dev["username"];
  $password = $dev["password"];
  $database = $dev["database"];
}

$conn = mysqli_connect($server,$username,$password, $database);
if (mysqli_connect_errno())
{
  echo "Failed to connect to MySQL: " . mysqli_connect_error();
}

$mysqli = new mysqli($server,$username,$password, $database);

if ($mysqli->connect_error) {
    die("Koneksi gagal: " . $mysqli->connect_error);
}

?>
