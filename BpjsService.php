<?php 
  require 'vendor/autoload.php';
  use GuzzleHttp\Client;
  
  class BpjsService {
    private $config;
    
    function __construct() {
      $this->init();
    }
    
    private function init() {
      $this->config = parse_ini_file("config.cnf", true);
      $this->config = $this->config["BPJServices2"];
    }
    
    function request($path,$data,$type){
      $client = new Client();
      $url = $this->config["url"].$path;
      $conf = [
        'json' => $data
      ];
      try {
        $response = $client->request($type,$url,$conf);

        $result = json_decode($response->getBody());
        return $result;
      } catch (\Throwable $e) {
        return json_decode(json_encode([
          'metaData' => [
            'code' => 500,
            'message' => $e->getMessage()
          ],
        ]),FALSE);
      }  
    }
  }


