<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title ?? 'Kiosk RSKD') ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/css/jquery-ui.css">
    <link rel="stylesheet" href="/assets/css/wickedpicker.css">
    <link rel="stylesheet" href="/assets/css/sweetalert.min.css">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/app.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/assets/images/logo.png">
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px 0;
        }
        
        .kiosk-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .kiosk-header h1 {
            background: hsla(0, 0%, 100%, 0.9);
            display: inline-block;
            padding: 20px 40px;
            border-radius: 15px;
            color: #267b84;
            font-weight: bold;
            font-size: 3.5rem;
            margin: 0;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .kiosk-header img {
            width: 80px;
            height: auto;
            margin-left: 20px;
            vertical-align: middle;
        }
        
        .main-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .btn-kiosk {
            font-size: 1.5rem;
            font-weight: bold;
            padding: 15px 30px;
            border-radius: 10px;
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-kiosk:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .keypad-btn {
            width: 100%;
            height: 80px;
            font-size: 2rem;
            font-weight: bold;
            margin: 5px;
            border-radius: 10px;
            border: none;
            background: #ffc107;
            color: #000;
            transition: all 0.2s ease;
        }
        
        .keypad-btn:hover {
            background: #ffb300;
            transform: scale(1.05);
        }
        
        .keypad-btn:active {
            transform: scale(0.95);
        }
        
        .mr-input {
            text-align: center;
            height: 80px;
            font-size: 2.5rem;
            font-weight: bold;
            border: 3px solid #267b84;
            border-radius: 10px;
            background: #fff;
        }
        
        .mr-input:focus {
            border-color: #1e5f66;
            box-shadow: 0 0 0 0.2rem rgba(38, 123, 132, 0.25);
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            color: #fff;
            font-size: 1.1rem;
        }
        
        .alert-kiosk {
            font-size: 1.2rem;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 6px solid #f3f3f3;
            border-top: 6px solid #267b84;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .kiosk-header h1 {
                font-size: 2.5rem;
                padding: 15px 25px;
            }
            
            .main-container {
                margin: 0 15px;
                padding: 20px;
            }
            
            .keypad-btn {
                height: 60px;
                font-size: 1.5rem;
            }
            
            .mr-input {
                height: 60px;
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <!-- Header -->
    <div class="kiosk-header">
        <h1>
            KIOSK 
            <img src="/assets/images/logo.png" alt="Logo RSKD">
        </h1>
    </div>

    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash'])): ?>
        <?php foreach ($_SESSION['flash'] as $type => $message): ?>
            <div class="container">
                <div class="alert alert-<?= $type === 'error' ? 'danger' : $type ?> alert-kiosk alert-dismissible fade show">
                    <?= htmlspecialchars($message) ?>
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            </div>
        <?php endforeach; ?>
        <?php unset($_SESSION['flash']); ?>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="container">
        <div class="main-container">
            <?= $content ?? '' ?>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>&copy; <?= date('Y') ?> SIMRS - Aplikasi Pendaftaran Mandiri. All Rights Reserved</p>
    </div>

    <!-- JavaScript -->
    <script src="/assets/js/jquery-2.1.4.min.js"></script>
    <script src="/assets/js/jquery-ui.js"></script>
    <script src="/assets/js/bootstrap.min.js"></script>
    <script src="/assets/js/sweetalert.min.js"></script>
    <script src="/assets/js/wickedpicker.js"></script>
    <script src="/assets/js/app.js"></script>

    <script>
        // Global functions
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showAlert(type, title, message, timer = 3000) {
            swal({
                icon: type,
                title: title,
                text: message,
                timer: timer,
                button: false
            });
        }

        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);

        // Prevent form double submission
        $('form').on('submit', function() {
            $(this).find('button[type="submit"]').prop('disabled', true);
            showLoading();
        });
    </script>

    <?= $additionalJs ?? '' ?>
</body>
</html>
