.page-content {
  $mobile-breakpoint: 880px;
}
.page-content table {
  border-collapse: collapse;
  border: none;
  width: 100%;
}
.page-content th {
  font-size: 17px;
  color: rgba(0,0,0,0.34);
  padding: 20px 15px;
  text-transform: capitalize;
  font-weight: 400;
}
.page-content thead > tr {
  border-bottom: 2px solid rgba(0,0,0,0.1);
}
.page-content tr {
  text-align: left;
  box-shadow: 0px -1px 0px rgba(0,0,0,0.15);
}
.page-content tr:first-child {
  box-shadow: none;
}
.page-content td {
  padding: 17px;
}
.page-content td:first-child > code {
  color: #2e9fef;
  background: none;
  border: none;
  font-size: 16px;
  padding: 0;
}
@media all and (min-width: mobile-breakpoint) {
  .page-content tbody tr:nth-child(1) {
    box-shadow: none;
  }
}
@media all and (max-width: mobile-breakpoint) {
  .page-content table,
  .page-content thead,
  .page-content tbody,
  .page-content th,
  .page-content td,
  .page-content tr {
    display: block;
  }
  .page-content thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  .page-content tr {
    margin-top: -1px;
    box-shadow: 0px -1px 0px 0px rgba(0,0,0,0.27);
  }
  .page-content td {
/* Behave  like a "row" */
    border: none;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-left: mobile-padding;
    min-height: 20px;
  }
  .page-content td::before {
/* Now like a table header */
    position: absolute;
/* Top/left values mimic padding */
    top: 15px;
    left: 15px;
    width: mobile-padding;
    width: calc((mobile-padding - 35px));
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 10px;
    white-space: nowrap;
    color: rgba(0,0,0,0.54);
    font-family: page-font;
    font-size: 16px;
    text-transform: capitalize;
    content: attr(data-name);
  }
}
