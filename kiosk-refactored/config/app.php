<?php

return [
    'name' => $_ENV['APP_NAME'] ?? 'Kiosk Pendaftaran RSKD',
    'env' => $_ENV['APP_ENV'] ?? 'production',
    'debug' => ($_ENV['APP_DEBUG'] ?? 'false') === 'true',
    'key' => $_ENV['APP_KEY'] ?? 'default_app_key',
    'timezone' => 'Asia/Jakarta',
    
    'session' => [
        'lifetime' => (int) ($_ENV['SESSION_LIFETIME'] ?? 120), // minutes
        'cookie_name' => 'kiosk_session',
        'cookie_secure' => false, // Set to true for HTTPS
        'cookie_httponly' => true,
        'cookie_samesite' => 'Lax'
    ],
    
    'security' => [
        'csrf_token_name' => 'csrf_token',
        'max_login_attempts' => 5,
        'lockout_duration' => 15, // minutes
        'password_min_length' => 8
    ],
    
    'logging' => [
        'level' => $_ENV['LOG_LEVEL'] ?? 'error',
        'file' => $_ENV['LOG_FILE'] ?? __DIR__ . '/../logs/app.log',
        'max_size' => 10 * 1024 * 1024, // 10MB
        'max_files' => 5
    ],
    
    'cache' => [
        'default' => 'file',
        'stores' => [
            'file' => [
                'driver' => 'file',
                'path' => __DIR__ . '/../storage/cache'
            ]
        ]
    ],
    
    'print' => [
        'enabled' => ($_ENV['PRINT_ENABLED'] ?? 'true') === 'true',
        'timeout' => (int) ($_ENV['PRINT_TIMEOUT'] ?? 10000), // milliseconds
        'default_copies' => 1
    ],
    
    'bpjs' => [
        'timeout' => 30, // seconds
        'retry_attempts' => 3,
        'rujukan_max_days' => 90
    ],
    
    'validation' => [
        'mr_min_length' => 1,
        'mr_max_length' => 10,
        'mr_pattern' => '/^[0-9]+$/'
    ]
];
