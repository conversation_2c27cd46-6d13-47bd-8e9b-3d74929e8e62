<?php

// Bootstrap aplikasi
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();
}

// Error reporting berdasarkan environment
if (($_ENV['APP_DEBUG'] ?? false) === 'true') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set timezone
date_default_timezone_set('Asia/Jakarta');

// Start session
session_start();

// Simple router
$requestUri = $_SERVER['REQUEST_URI'];
$requestMethod = $_SERVER['REQUEST_METHOD'];

// Remove query string
$path = parse_url($requestUri, PHP_URL_PATH);

// Remove base path if exists
$basePath = dirname($_SERVER['SCRIPT_NAME']);
if ($basePath !== '/') {
    $path = substr($path, strlen($basePath));
}

// Route handling
try {
    switch ($path) {
        case '/':
        case '/home':
            $controller = new App\Controllers\HomeController();
            $controller->index();
            break;

        case '/home/<USER>':
            $controller = new App\Controllers\HomeController();
            $controller->processInput();
            break;

        case '/home/<USER>':
            $controller = new App\Controllers\HomeController();
            $controller->validateMr();
            break;

        case '/home/<USER>':
            $controller = new App\Controllers\HomeController();
            $controller->help();
            break;

        case '/home/<USER>':
            $controller = new App\Controllers\HomeController();
            $controller->reset();
            break;

        case '/registration/process':
            $controller = new App\Controllers\RegistrationController();
            $controller->process();
            break;

        case '/registration/submit':
            $controller = new App\Controllers\RegistrationController();
            $controller->submit();
            break;

        case '/print/sep':
            $controller = new App\Controllers\PrintController();
            $controller->sep();
            break;

        case '/api/health':
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'OK',
                'timestamp' => date('Y-m-d H:i:s'),
                'version' => '2.0.0'
            ]);
            break;

        default:
            // 404 Not Found
            http_response_code(404);
            include __DIR__ . '/../app/Views/errors/404.php';
            break;
    }

} catch (Exception $e) {
    // Error handling
    error_log("Application Error: " . $e->getMessage());
    
    if (($_ENV['APP_DEBUG'] ?? false) === 'true') {
        echo "<h1>Application Error</h1>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        http_response_code(500);
        include __DIR__ . '/../app/Views/errors/500.php';
    }
}
