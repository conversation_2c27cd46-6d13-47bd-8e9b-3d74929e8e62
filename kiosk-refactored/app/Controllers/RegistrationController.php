<?php

namespace App\Controllers;

use App\Models\Patient;
use App\Models\BpjsData;
use App\Services\DatabaseService;
use App\Services\BpjsService;

/**
 * Controller untuk proses pendaftaran
 */
class RegistrationController extends BaseController
{
    private $patientModel;
    private $bpjsModel;
    private $bpjsService;

    public function __construct()
    {
        parent::__construct();
        $db = new DatabaseService();
        $this->patientModel = new Patient($db);
        $this->bpjsModel = new BpjsData($db);
        $this->bpjsService = new BpjsService();
    }

    /**
     * Proses pendaftaran berdasarkan nomor MR
     */
    public function process()
    {
        try {
            $nomr = $this->sanitizeInput($_GET['nomr'] ?? '');

            if (empty($nomr)) {
                $this->setFlashMessage('error', 'Nomor MR tidak valid');
                $this->redirect('/');
                return;
            }

            // Cek apakah pasien adalah peserta BPJS
            $patientData = $this->patientModel->findByMr($nomr);
            
            if (!$patientData) {
                $this->showError('Tidak bisa didaftarkan, bukan pasien BPJS.');
                return;
            }

            // Cek apakah sudah terdaftar hari ini
            $todayRegistration = $this->patientModel->checkTodayRegistration($nomr);
            
            if ($todayRegistration) {
                $this->showExistingRegistration($todayRegistration);
                return;
            }

            // Cek tagihan yang masih aktif
            $activeTagihan = $this->patientModel->checkActiveTagihan($nomr);
            
            if ($activeTagihan) {
                $this->patientModel->insertLogApm($nomr, $activeTagihan['ID']);
                $this->showError('Anda belum bisa mendaftar. Silakan ambil antrian.');
                return;
            }

            // Proses validasi BPJS dan rujukan
            $this->processBpjsValidation($nomr, $patientData);

        } catch (\Exception $e) {
            $this->logError("RegistrationController::process Error: " . $e->getMessage());
            $this->showError('Terjadi kesalahan sistem. Silakan coba lagi.');
        }
    }

    /**
     * Proses validasi BPJS dan rujukan
     */
    private function processBpjsValidation($nomr, $patientData)
    {
        $kartubpjs = $patientData['kartubpjs'];

        // Cek kepesertaan BPJS
        $pesertaResult = $this->bpjsService->cariPeserta($kartubpjs);
        
        if ($pesertaResult->metaData->code !== 200) {
            $this->showError('Data peserta BPJS tidak ditemukan.');
            return;
        }

        $statusPeserta = $this->bpjsService->getStatusPeserta($pesertaResult);
        
        if (!$statusPeserta['valid']) {
            $this->showError('Status peserta BPJS: ' . $statusPeserta['message']);
            return;
        }

        // Cek rujukan
        $rujukanResult = $this->checkRujukan($kartubpjs);
        
        if (!$rujukanResult['valid']) {
            $this->showError($rujukanResult['message']);
            return;
        }

        // Cek perjanjian
        $appointment = $this->patientModel->getAppointmentDetail($nomr);
        
        if (!$appointment) {
            $this->showError('Tidak ada perjanjian hari ini.');
            return;
        }

        // Tampilkan form pendaftaran
        $this->showRegistrationForm($nomr, $patientData, $pesertaResult, $rujukanResult['data'], $appointment);
    }

    /**
     * Cek rujukan BPJS
     */
    private function checkRujukan($kartubpjs)
    {
        // Cek rujukan faskes 2 dulu
        $rujukanFaskes2 = $this->bpjsService->cariRujukanFaskes2($kartubpjs);
        
        if ($rujukanFaskes2->metaData->code === 200) {
            $rujukan = $rujukanFaskes2->response->rujukan[0];
            
            // Validasi masa berlaku rujukan
            if (!$this->bpjsService->validateRujukan($rujukan->tglKunjungan)) {
                return [
                    'valid' => false,
                    'message' => 'Masa berlaku rujukan sudah habis (Faskes 2).'
                ];
            }
            
            return [
                'valid' => true,
                'data' => $rujukan,
                'source' => 'faskes2'
            ];
        }

        // Jika tidak ada rujukan faskes 2, cek faskes 1
        $rujukanFaskes1 = $this->bpjsService->cariRujukanFaskes1($kartubpjs);
        
        if ($rujukanFaskes1->metaData->code === 200) {
            $rujukan = $rujukanFaskes1->response->rujukan[0];
            
            if (!$rujukan->tglKunjungan) {
                return [
                    'valid' => false,
                    'message' => 'Masa berlaku rujukan sudah habis (Faskes 1).'
                ];
            }
            
            // Validasi masa berlaku rujukan
            if (!$this->bpjsService->validateRujukan($rujukan->tglKunjungan)) {
                return [
                    'valid' => false,
                    'message' => 'Masa berlaku rujukan sudah lebih dari 3 bulan.'
                ];
            }
            
            return [
                'valid' => true,
                'data' => $rujukan,
                'source' => 'faskes1'
            ];
        }

        return [
            'valid' => false,
            'message' => 'Rujukan tidak ditemukan.'
        ];
    }

    /**
     * Tampilkan form pendaftaran
     */
    private function showRegistrationForm($nomr, $patientData, $pesertaResult, $rujukanData, $appointment)
    {
        $data = [
            'title' => 'Form Pendaftaran',
            'nomr' => $nomr,
            'patient_data' => $patientData,
            'peserta_data' => $pesertaResult->response->peserta,
            'rujukan_data' => $rujukanData,
            'appointment_data' => $appointment,
            'csrf_token' => $this->generateCsrfToken()
        ];

        $this->render('registration/form', $data);
    }

    /**
     * Tampilkan registrasi yang sudah ada
     */
    private function showExistingRegistration($registration)
    {
        $data = [
            'title' => 'Registrasi Sudah Ada',
            'registration' => $registration
        ];

        $this->render('registration/existing', $data);
    }

    /**
     * Tampilkan pesan error
     */
    private function showError($message, $autoRedirect = true)
    {
        $data = [
            'title' => 'Informasi',
            'message' => $message,
            'auto_redirect' => $autoRedirect,
            'redirect_url' => '/',
            'redirect_delay' => 3000
        ];

        $this->render('common/error', $data);
    }

    /**
     * Submit pendaftaran
     */
    public function submit()
    {
        try {
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->redirect('/');
                return;
            }

            // Validasi CSRF token
            $csrfToken = $_POST['csrf_token'] ?? '';
            if (!$this->validateCsrfToken($csrfToken)) {
                $this->showError('Token keamanan tidak valid');
                return;
            }

            // Proses data pendaftaran
            $formData = $this->sanitizeInput($_POST);
            
            // Validasi required fields
            $requiredFields = ['nomr', 'nobpjs', 'rujukan', 'poliTujuan'];
            $errors = $this->validateRequired($formData, $requiredFields);
            
            if (!empty($errors)) {
                $this->showError('Data tidak lengkap: ' . implode(', ', $errors));
                return;
            }

            // Generate SEP
            $sepResult = $this->generateSep($formData);
            
            if ($sepResult['success']) {
                $this->redirect('/print/sep?sep=' . urlencode($sepResult['sep_number']));
            } else {
                $this->showError($sepResult['message']);
            }

        } catch (\Exception $e) {
            $this->logError("RegistrationController::submit Error: " . $e->getMessage());
            $this->showError('Terjadi kesalahan saat memproses pendaftaran.');
        }
    }

    /**
     * Generate SEP
     */
    private function generateSep($formData)
    {
        try {
            $sepData = [
                'noKartu' => $formData['nobpjs'],
                'tglSep' => date('Y-m-d'),
                'jnsPelayanan' => '2', // Rawat jalan
                'klsRawat' => $formData['kelasRawat'],
                'norm' => $formData['nomr'],
                'tglRujukan' => $formData['tglRujukan'],
                'noRujukan' => $formData['rujukan'],
                'ppkRujukan' => $formData['ppkrujukan'],
                'diagAwal' => $formData['diagawal'],
                'poliTujuan' => $formData['poliTujuan'],
                'dpjp' => $formData['idhafis'],
                'noKontrol' => $formData['nokontrol'],
                'user' => 'KIOSK_USER'
            ];

            $result = $this->bpjsService->generateSep($sepData);
            
            if ($result->metaData->code === 200) {
                // Simpan data SEP ke database
                $this->bpjsModel->saveSep([
                    'noSEP' => $result->response->sep->noSep,
                    'noKartu' => $sepData['noKartu'],
                    'norm' => $sepData['norm'],
                    'tglSEP' => $sepData['tglSep'],
                    'jnsPelayanan' => $sepData['jnsPelayanan'],
                    'klsRawat' => $sepData['klsRawat'],
                    'noRujukan' => $sepData['noRujukan'],
                    'ppkRujukan' => $sepData['ppkRujukan'],
                    'diagAwal' => $sepData['diagAwal'],
                    'poliTujuan' => $sepData['poliTujuan'],
                    'dpjp' => $sepData['dpjp'],
                    'noKontrol' => $sepData['noKontrol'],
                    'user' => $sepData['user']
                ]);

                return [
                    'success' => true,
                    'sep_number' => $result->response->sep->noSep
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $result->metaData->message ?? 'Gagal generate SEP'
                ];
            }

        } catch (\Exception $e) {
            $this->logError("Generate SEP Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Terjadi kesalahan saat generate SEP'
            ];
        }
    }
}
