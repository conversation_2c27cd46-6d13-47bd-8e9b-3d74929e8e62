<?php
	$sep = $_GET['sep'];
?>
<script type="text/javascript" src="js/jquery-2.1.4.min.js"></script>
<script src="assets/printdoc.js"></script>
<script>
	var cetak = "bpjs.CetakSEP_1";
	var sep = "<?php echo $sep; ?>";
	window.requestPrint({
		NAME:cetak,
		TYPE:'Pdf',
		EXT:'docx',
		PARAMETER:{
			PSEP:sep,CETAK_HEADER:1
		},
		REQUEST_FOR_PRINT:false,
		PRINT_NAME:"CetakSEP",
		URL:true,
		CONNECTION_NUMBER:0,
		COPIES:1,
		id:"data.model.RequestReport-1"
	});
</script>
