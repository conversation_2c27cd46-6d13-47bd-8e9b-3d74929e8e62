<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Exception;

/**
 * BPJS Service untuk integrasi dengan web service BPJS
 */
class BpjsService
{
    private $client;
    private $config;

    public function __construct()
    {
        $this->loadConfig();
        $this->client = new Client([
            'timeout' => 30,
            'verify' => false
        ]);
    }

    /**
     * Load konfigurasi BPJS dari environment
     */
    private function loadConfig()
    {
        $this->config = [
            'url' => $_ENV['BPJS_URL'] ?? 'http://localhost/webservice/',
            'url_v2' => $_ENV['BPJS_URL_V2'] ?? 'http://localhost/webservice/',
            'key' => $_ENV['BPJS_KEY'] ?? 'default_key',
            'secret' => $_ENV['BPJS_SECRET'] ?? 'default_secret'
        ];
    }

    /**
     * Melakukan request ke BPJS web service
     */
    public function request($path, $data = [], $method = 'GET')
    {
        try {
            $url = $this->config['url_v2'] . $path;
            
            $options = [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ]
            ];

            if ($method === 'GET' && !empty($data)) {
                $url .= '?' . http_build_query($data);
            } elseif ($method === 'POST') {
                $options['json'] = $data;
            }

            $response = $this->client->request($method, $url, $options);
            $result = json_decode($response->getBody()->getContents());

            return $result;

        } catch (RequestException $e) {
            error_log("BPJS Request Error: " . $e->getMessage());
            
            return (object) [
                'metaData' => (object) [
                    'code' => 500,
                    'message' => 'Error connecting to BPJS service: ' . $e->getMessage()
                ]
            ];
        } catch (Exception $e) {
            error_log("BPJS Service Error: " . $e->getMessage());
            
            return (object) [
                'metaData' => (object) [
                    'code' => 500,
                    'message' => 'Internal error: ' . $e->getMessage()
                ]
            ];
        }
    }

    /**
     * Cari peserta berdasarkan nomor kartu BPJS
     */
    public function cariPeserta($noKartu, $tglSep = null)
    {
        $tglSep = $tglSep ?: date('Y-m-d');
        
        return $this->request('vclaimv2/peserta', [
            'noKartu' => $noKartu,
            'tglSEP' => $tglSep
        ], 'GET');
    }

    /**
     * Cari rujukan dari faskes 2 (RS)
     */
    public function cariRujukanFaskes2($noKartu)
    {
        return $this->request('vclaimv2/rujukan/rs', [
            'noKartu' => $noKartu
        ], 'GET');
    }

    /**
     * Cari rujukan dari faskes 1 (Pcare)
     */
    public function cariRujukanFaskes1($noKartu)
    {
        return $this->request('vclaimv2/rujukan/pcare', [
            'noKartu' => $noKartu
        ], 'GET');
    }

    /**
     * Generate SEP (Surat Eligibilitas Peserta)
     */
    public function generateSep($data)
    {
        $sepData = [
            'noKartu' => $data['noKartu'],
            'tglSep' => $data['tglSep'],
            'jnsPelayanan' => $data['jnsPelayanan'],
            'klsRawat' => $data['klsRawat'],
            'norm' => $data['norm'],
            'asalRujukan' => $data['asalRujukan'] ?? '2',
            'tglRujukan' => $data['tglRujukan'],
            'noRujukan' => $data['noRujukan'],
            'ppkRujukan' => $data['ppkRujukan'],
            'catatan' => $data['catatan'] ?? '-',
            'diagAwal' => $data['diagAwal'],
            'poliTujuan' => $data['poliTujuan'],
            'ekseKutif' => $data['ekseKutif'] ?? '0',
            'cob' => $data['cob'] ?? '0',
            'katarak' => $data['katarak'] ?? '0',
            'tujuanKunj' => $data['tujuanKunj'] ?? '0',
            'flagProcedure' => $data['flagProcedure'] ?? '',
            'kdPenunjang' => $data['kdPenunjang'] ?? '',
            'assesmentPel' => $data['assesmentPel'] ?? '4',
            'lakaLantas' => $data['lakaLantas'] ?? '2',
            'penjamin' => $data['penjamin'] ?? '',
            'lokasiLaka' => $data['lokasiLaka'] ?? '-',
            'noKontrol' => $data['noKontrol'],
            'dpjp' => $data['dpjp'],
            'noKontak' => $data['noKontak'] ?? '',
            'user' => $data['user']
        ];

        return $this->request('vclaimv2/sep', $sepData, 'POST');
    }

    /**
     * Validasi rujukan berdasarkan tanggal
     */
    public function validateRujukan($tglRujukan, $maxDays = 90)
    {
        $dateRujukan = new \DateTime($tglRujukan);
        $dateNow = new \DateTime();
        $diff = $dateNow->diff($dateRujukan)->days;

        return $diff <= $maxDays;
    }

    /**
     * Get status peserta yang user-friendly
     */
    public function getStatusPeserta($pesertaData)
    {
        if (!isset($pesertaData->response->peserta)) {
            return [
                'valid' => false,
                'message' => 'Data peserta tidak ditemukan'
            ];
        }

        $peserta = $pesertaData->response->peserta;
        $status = $peserta->statusPeserta->keterangan ?? 'TIDAK AKTIF';

        return [
            'valid' => $status === 'AKTIF',
            'message' => $status,
            'hakKelas' => $peserta->hakKelas->keterangan ?? 'TIDAK DIKETAHUI'
        ];
    }
}
