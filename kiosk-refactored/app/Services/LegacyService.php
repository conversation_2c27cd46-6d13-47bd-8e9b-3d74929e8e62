<?php

namespace App\Services;

/**
 * Legacy Service untuk backward compatibility dengan service lama
 * Digunakan untuk integrasi dengan sistem SIMRS yang sudah ada
 */
class LegacyService
{
    private $config;

    public function __construct()
    {
        $this->loadConfig();
    }

    /**
     * Load konfigurasi dari environment
     */
    private function loadConfig()
    {
        $this->config = [
            'url' => $_ENV['BPJS_URL'] ?? 'http://localhost/webservice/',
            'key' => $_ENV['BPJS_KEY'] ?? '2017RsdjServiceSimpel3212'
        ];
    }

    /**
     * Send request menggunakan cURL (legacy method)
     */
    private function sendRequest($action = "", $method = "GET", $data = "", $contentType = "application/json", $url = "")
    {
        $curl = curl_init();
        $url = ($url == '' ? $this->config["url"] . $action : $url);
        $headers = array();
        
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        
        $headers[] = "Key: " . $this->config['key'];
        $headers[] = "Content-type: " . $contentType;
        $headers[] = "Content-length: " . strlen($data);
        
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($curl);
        $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

        curl_close($curl);

        $result = json_decode($result);
        
        if ($http_status !== 200) {
            $result = (object) [
                'success' => false,
                'code' => $http_status,
                'message' => isset($result->detail) ? $result->detail : 'Unknown error'
            ];
        }

        return $result;
    }

    /**
     * Cari peserta dengan nomor kartu BPJS (legacy method)
     */
    public function cariPesertaDgnNoKartuBPJS($nomor, $tglSep)
    {
        $tgl = isset($tglSep) ? $tglSep : date("Y-m-d");
        return $this->sendRequest("webservice/plugins/bpjs/" . $nomor . "?tglSEP=" . $tgl);
    }

    /**
     * Cari rujukan dengan nomor kartu BPJS (legacy method)
     */
    public function cariRujukanDgnNoKartuBPJS($nomor)
    {
        return $this->sendRequest("webservice/plugins/bpjs/cariRujukanDgnNoKartuBPJS/" . $nomor);
    }

    /**
     * Generate SEP (legacy method)
     */
    public function generateNoSEP($noKartu, $tglSep, $asalRujukan = "2", $tglRujukan, $noRujukan, $ppkRujukan,
        $jnsPelayanan, $catatan = "-", $diagAwal, $poliTujuan, $eksekutif, $cob = "0", $noTelp = "", $user, $noMr, 
        $create, $ip, $lakaLantas = "2", $lokasiLaka = "-", $penjamin = "", $noKontrol, $dpjp, $kelasRawat)
    {
        $params = [
            "noKartu" => $noKartu,
            "tglSep" => $tglSep,
            "jnsPelayanan" => $jnsPelayanan,
            "klsRawat" => $kelasRawat,
            "norm" => $noMr,
            "asalRujukan" => $asalRujukan,
            "tglRujukan" => $tglRujukan,
            "noRujukan" => $noRujukan,
            "ppkRujukan" => $ppkRujukan,
            "catatan" => $catatan,
            "diagAwal" => $diagAwal,
            "poliTujuan" => $poliTujuan,
            "ekseKutif" => $eksekutif,
            "cob" => $cob,
            "katarak" => 0,
            "tujuanKunj" => '0',
            "flagProcedure" => '',
            "kdPenunjang" => '',
            "assesmentPel" => '4',
            "lakaLantas" => $lakaLantas,
            "penjamin" => $penjamin,
            "lokasiLaka" => $lokasiLaka,
            "noKontrol" => $noKontrol,
            "dpjp" => $dpjp,
            "noKontak" => $noTelp,
            "user" => $user
        ];

        return $this->sendRequest("webservice/plugins/bpjs", "POST", json_encode($params));
    }

    /**
     * Pendaftaran (legacy method)
     */
    public function pendaftaran($params)
    {
        return $this->sendRequest("webservice/pendaftaran/kios/buatRegistrasiKios", "POST", $params);
    }

    /**
     * Rencana kontrol (legacy method)
     */
    public function rencanaKontrol($params)
    {
        return $this->sendRequest("webservice/plugins/bpjs/rencanaKontrol", "POST", $params);
    }
}
