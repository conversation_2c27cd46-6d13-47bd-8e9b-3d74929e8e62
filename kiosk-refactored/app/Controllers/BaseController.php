<?php

namespace App\Controllers;

/**
 * Base Controller dengan fungsi-fungsi umum
 */
abstract class BaseController
{
    protected $viewPath;

    public function __construct()
    {
        // Start session jika belum dimulai
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $this->viewPath = __DIR__ . '/../Views/';
    }

    /**
     * Render view dengan data
     */
    protected function render($view, $data = [])
    {
        // Extract data menjadi variables
        extract($data);

        // Include view file
        $viewFile = $this->viewPath . $view . '.php';
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            throw new \Exception("View file not found: " . $viewFile);
        }
    }

    /**
     * Redirect ke URL tertentu
     */
    protected function redirect($url)
    {
        header('Location: ' . $url);
        exit;
    }

    /**
     * Set flash message
     */
    protected function setFlashMessage($type, $message)
    {
        $_SESSION['flash'][$type] = $message;
    }

    /**
     * Get flash message
     */
    protected function getFlashMessage($type)
    {
        if (isset($_SESSION['flash'][$type])) {
            $message = $_SESSION['flash'][$type];
            unset($_SESSION['flash'][$type]);
            return $message;
        }
        return null;
    }

    /**
     * Get all flash messages
     */
    protected function getAllFlashMessages()
    {
        $messages = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $messages;
    }

    /**
     * Sanitize input data
     */
    protected function sanitizeInput($input)
    {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }
        
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Validate CSRF token
     */
    protected function validateCsrfToken($token)
    {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * Generate CSRF token
     */
    protected function generateCsrfToken()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * Return JSON response
     */
    protected function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Validate required fields
     */
    protected function validateRequired($data, $requiredFields)
    {
        $errors = [];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $errors[] = "Field {$field} is required";
            }
        }
        
        return $errors;
    }

    /**
     * Log error
     */
    protected function logError($message, $context = [])
    {
        $logMessage = date('Y-m-d H:i:s') . " - " . $message;
        
        if (!empty($context)) {
            $logMessage .= " - Context: " . json_encode($context);
        }
        
        error_log($logMessage);
    }

    /**
     * Check if request is AJAX
     */
    protected function isAjaxRequest()
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Get current URL
     */
    protected function getCurrentUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    }

    /**
     * Get base URL
     */
    protected function getBaseUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        return $protocol . '://' . $_SERVER['HTTP_HOST'];
    }

    /**
     * Format currency
     */
    protected function formatCurrency($amount)
    {
        return 'Rp ' . number_format($amount, 0, ',', '.');
    }

    /**
     * Format date
     */
    protected function formatDate($date, $format = 'd/m/Y')
    {
        if (empty($date)) return '-';
        
        try {
            $dateObj = new \DateTime($date);
            return $dateObj->format($format);
        } catch (\Exception $e) {
            return $date;
        }
    }

    /**
     * Format datetime
     */
    protected function formatDateTime($datetime, $format = 'd/m/Y H:i')
    {
        if (empty($datetime)) return '-';
        
        try {
            $dateObj = new \DateTime($datetime);
            return $dateObj->format($format);
        } catch (\Exception $e) {
            return $datetime;
        }
    }
}
