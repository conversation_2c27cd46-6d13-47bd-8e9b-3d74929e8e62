<?php

namespace App\Services;

use mysqli;
use Exception;

/**
 * Database Service untuk menangani koneksi dan operasi database
 */
class DatabaseService
{
    private $connection;
    private $config;

    public function __construct()
    {
        $this->loadConfig();
        $this->connect();
    }

    /**
     * Load konfigurasi database dari environment
     */
    private function loadConfig()
    {
        $this->config = [
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? 3306,
            'database' => $_ENV['DB_DATABASE'] ?? 'simrs',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? ''
        ];
    }

    /**
     * Membuat koneksi ke database
     */
    private function connect()
    {
        try {
            $this->connection = new mysqli(
                $this->config['host'],
                $this->config['username'],
                $this->config['password'],
                $this->config['database'],
                $this->config['port']
            );

            if ($this->connection->connect_error) {
                throw new Exception("Koneksi database gagal: " . $this->connection->connect_error);
            }

            // Set charset
            $this->connection->set_charset("utf8");
        } catch (Exception $e) {
            error_log("Database connection error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Mendapatkan koneksi database
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * Execute query dengan prepared statement
     */
    public function query($sql, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($sql);
            
            if (!$stmt) {
                throw new Exception("Prepare statement gagal: " . $this->connection->error);
            }

            if (!empty($params)) {
                $types = str_repeat('s', count($params));
                $stmt->bind_param($types, ...$params);
            }

            $stmt->execute();
            $result = $stmt->get_result();
            
            return $result;
        } catch (Exception $e) {
            error_log("Database query error: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Fetch single row
     */
    public function fetchOne($sql, $params = [])
    {
        $result = $this->query($sql, $params);
        return $result ? $result->fetch_assoc() : null;
    }

    /**
     * Fetch multiple rows
     */
    public function fetchAll($sql, $params = [])
    {
        $result = $this->query($sql, $params);
        $rows = [];
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $rows[] = $row;
            }
        }
        
        return $rows;
    }

    /**
     * Insert data dan return last insert id
     */
    public function insert($table, $data)
    {
        $columns = implode(',', array_keys($data));
        $placeholders = str_repeat('?,', count($data) - 1) . '?';
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $this->query($sql, array_values($data));
        
        return $this->connection->insert_id;
    }

    /**
     * Update data
     */
    public function update($table, $data, $where, $whereParams = [])
    {
        $setParts = [];
        foreach (array_keys($data) as $column) {
            $setParts[] = "{$column} = ?";
        }
        $setClause = implode(', ', $setParts);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge(array_values($data), $whereParams);
        
        $this->query($sql, $params);
        
        return $this->connection->affected_rows;
    }

    /**
     * Close connection
     */
    public function close()
    {
        if ($this->connection) {
            $this->connection->close();
        }
    }

    /**
     * Destructor
     */
    public function __destruct()
    {
        $this->close();
    }
}
