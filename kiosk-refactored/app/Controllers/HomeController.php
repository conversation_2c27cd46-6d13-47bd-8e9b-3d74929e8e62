<?php

namespace App\Controllers;

use App\Models\Patient;
use App\Services\DatabaseService;

/**
 * Controller untuk halaman utama kiosk
 */
class HomeController extends BaseController
{
    private $patientModel;

    public function __construct()
    {
        parent::__construct();
        $this->patientModel = new Patient(new DatabaseService());
    }

    /**
     * <PERSON><PERSON>lkan halaman utama dengan keypad input
     */
    public function index()
    {
        $data = [
            'title' => 'Pendaftaran Mandiri RSKD',
            'app_name' => $_ENV['APP_NAME'] ?? 'KIOSK RSKD'
        ];

        $this->render('home/index', $data);
    }

    /**
     * Proses input nomor MR
     */
    public function processInput()
    {
        try {
            // Validasi request method
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->redirect('/');
                return;
            }

            // Validasi input
            $nomr = $this->sanitizeInput($_POST['nomr'] ?? '');
            $key = $_POST['key'] ?? '';

            // Validasi key untuk security
            if ($key !== 'crtghb68p2xb6jc44gcdx9jhm') {
                $this->setFlashMessage('error', 'Akses tidak valid');
                $this->redirect('/');
                return;
            }

            // Validasi nomor MR
            if (empty($nomr)) {
                $this->setFlashMessage('error', 'Nomor MR tidak boleh kosong');
                $this->redirect('/');
                return;
            }

            if (!$this->patientModel->validateMr($nomr)) {
                $this->setFlashMessage('error', 'Format nomor MR tidak valid');
                $this->redirect('/');
                return;
            }

            // Redirect ke registration controller
            $this->redirect('/registration/process?nomr=' . urlencode($nomr));

        } catch (\Exception $e) {
            error_log("HomeController::processInput Error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Terjadi kesalahan sistem');
            $this->redirect('/');
        }
    }

    /**
     * API endpoint untuk validasi nomor MR (AJAX)
     */
    public function validateMr()
    {
        header('Content-Type: application/json');

        try {
            $nomr = $this->sanitizeInput($_POST['nomr'] ?? '');

            if (empty($nomr)) {
                echo json_encode([
                    'valid' => false,
                    'message' => 'Nomor MR tidak boleh kosong'
                ]);
                return;
            }

            $isValid = $this->patientModel->validateMr($nomr);

            echo json_encode([
                'valid' => $isValid,
                'message' => $isValid ? 'Nomor MR valid' : 'Format nomor MR tidak valid'
            ]);

        } catch (\Exception $e) {
            error_log("HomeController::validateMr Error: " . $e->getMessage());
            echo json_encode([
                'valid' => false,
                'message' => 'Terjadi kesalahan sistem'
            ]);
        }
    }

    /**
     * Halaman bantuan/informasi
     */
    public function help()
    {
        $data = [
            'title' => 'Bantuan - Pendaftaran Mandiri RSKD'
        ];

        $this->render('home/help', $data);
    }

    /**
     * Reset session (untuk debugging)
     */
    public function reset()
    {
        session_destroy();
        $this->redirect('/');
    }
}
