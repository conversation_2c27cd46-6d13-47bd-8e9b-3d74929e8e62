<?php

return [
    'default' => 'mysql',
    
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? 3306,
            'database' => $_ENV['DB_DATABASE'] ?? 'simrs',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET sql_mode='STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'"
            ]
        ],
        
        'testing' => [
            'driver' => 'mysql',
            'host' => $_ENV['DB_TEST_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_TEST_PORT'] ?? 3306,
            'database' => $_ENV['DB_TEST_DATABASE'] ?? 'simrs_test',
            'username' => $_ENV['DB_TEST_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_TEST_PASSWORD'] ?? '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci'
        ]
    ],
    
    'pool' => [
        'min_connections' => 1,
        'max_connections' => 10,
        'connection_timeout' => 30,
        'idle_timeout' => 300
    ],
    
    'backup' => [
        'enabled' => false,
        'path' => __DIR__ . '/../storage/backups',
        'schedule' => '0 2 * * *' // Daily at 2 AM
    ]
];
