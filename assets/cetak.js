	Ext.Msg.show({
					title : _title,
					message : "Tekan tombol Yes/Ya utk cetak langsung<br/>Tekan tombol No/Tidak untuk Preview",
					buttons : Ext.Msg.YESNO,
					icon : Ext.Msg.QUESTION,
					defaultButton: 'yes',
					animateTarget : t,
					fn : function (btn) {
						if (btn != "yes") {
							_type = 'Pdf';
							_ext = 'pdf';
							_print = false;
						}
						
				view.cetak({
					NAME: 'layanan.CetakHasilRad',
					TYPE: _type,
					EXT: _ext,
					PARAMETER: {
						PTINDAKAN: rec.get('TINDAKAN_MEDIS')
					},
					REQUEST_FOR_PRINT: _print,
					PRINT_NAME: 'CetakHasil'
				},
				function(url) {
							view.openDialog('Print Preview', true, 0, 0, {
								xtype: 'panel',
								title: _title
							}, function(event, win) {
								win.down('panel').update("<iframe style='width: 100%; height: 100%' src='" + url + "'></iframe>");
							});
						});
				view.fireEvent('cetak', rec);
			}
				});