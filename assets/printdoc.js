if(window.requestPrint == undefined) {
	window.requestPrint = function(config, callback) {						
		var host = location.hostname + (location.port == '' ? '' : ':' + location.port);
		$.ajax({url: "http://***********/webservice/plugins/request-report",
			contentType: "application/json",
			type: "POST",
			data: JSON.stringify(config),
			success: function(result, status, xhr){
				if(status) {
					if (config.REQUEST_FOR_PRINT) {
						window.open('printdoc:' + result.content, '_self');							
					} else {
						if (config.URL) {
							window.location.href = result.url;
						}else{
							window.open(result.url);
						}
					}
				}
			}
		});
	}
}