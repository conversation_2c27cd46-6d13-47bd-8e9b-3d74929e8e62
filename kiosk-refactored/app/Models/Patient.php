<?php

namespace App\Models;

use App\Services\DatabaseService;

/**
 * Model untuk data pasien
 */
class Patient
{
    private $db;

    public function __construct(DatabaseService $db)
    {
        $this->db = $db;
    }

    /**
     * Cari pasien berdasarkan nomor MR
     */
    public function findByMr($nomr)
    {
        $sql = "SELECT pd.RUJUKAN rujukan, kap.NOMOR kartubpjs
                FROM pendaftaran.pendaftaran pd
                LEFT JOIN `master`.kartu_asuransi_pasien kap ON pd.NORM = kap.NORM AND kap.JENIS = 2 AND LENGTH(kap.NOMOR) = 13
                WHERE pd.NORM = ? AND pd.RUJUKAN IS NOT NULL
                ORDER BY pd.TANGGAL DESC
                LIMIT 1";

        return $this->db->fetchOne($sql, [$nomr]);
    }

    /**
     * Cek apakah pasien sudah terdaftar hari ini
     */
    public function checkTodayRegistration($nomr)
    {
        $sql = "SELECT pp.NOMOR, pp.NORM, pp.TANGGAL, tp.RUANGAN, pj.NOMOR SEP 
                FROM pendaftaran.pendaftaran pp 
                LEFT JOIN pendaftaran.penjamin pj ON pp.NOMOR = pj.NOPEN AND pj.JENIS = 2
                LEFT JOIN pendaftaran.tujuan_pasien tp ON pp.NOMOR = tp.NOPEN
                WHERE pp.NORM = ? AND pp.`STATUS` = '1' AND DATE(TANGGAL) = DATE(NOW())";

        return $this->db->fetchOne($sql, [$nomr]);
    }

    /**
     * Cek tagihan yang masih aktif
     */
    public function checkActiveTagihan($nomr)
    {
        $sql = "SELECT * FROM pembayaran.tagihan 
                WHERE pembayaran.tagihan.REF = ? AND pembayaran.tagihan.`STATUS` = '1'";

        return $this->db->fetchOne($sql, [$nomr]);
    }

    /**
     * Cek perjanjian pasien hari ini
     */
    public function getTodayAppointment($nomr)
    {
        $sql = "SELECT pp.NOMOR, pp.NORM, pp.TANGGAL, tp.RUANGAN, pj.NOMOR SEP 
                FROM pendaftaran.pendaftaran pp 
                LEFT JOIN pendaftaran.penjamin pj ON pp.NOMOR = pj.NOPEN AND pj.JENIS = 2
                LEFT JOIN pendaftaran.tujuan_pasien tp ON pp.NOMOR = tp.NOPEN
                WHERE pp.NORM = ? AND pp.`STATUS` = '1' AND DATE(TANGGAL) = DATE(NOW())";

        return $this->db->fetchOne($sql, [$nomr]);
    }

    /**
     * Get detail perjanjian pasien
     */
    public function getAppointmentDetail($nomr)
    {
        $sql = "SELECT NOMR, NAMAPASIEN, ID_DOKTER
                ,master.getNamalengkapPegawai(mdok.NIP) NAMADOKTER
                , mdok.HAFIS, rmp.TANGGAL TGLPERJANJIAN, NOKONTROL
                , ID_RUANGAN, mr.DESKRIPSI RUANGAN, mpeg.SMF
                , pr.RUANGAN_PENJAMIN, pb.nmsubspesialis NAMAPOLI
                , IF(STR_TO_DATE(rmj.AWAL - 1, '%H:%i:%s') < CURRENT_TIME() 
                , IF(STR_TO_DATE(rmj.AKHIR , '%H:%i:%s') - INTERVAL '30' MINUTE  > CURRENT_TIME(),1,1)
                , 2) WAKTU_MULAI
                , rmj.AWAL
                , rmj.AKHIR
                , rmp.RENCANA
                , IF (
                    STR_TO_DATE(rmj.AWAL, '%H:%i:%s') - INTERVAL 30 MINUTE < CURRENT_TIME()
                    AND STR_TO_DATE(rmj.AKHIR, '%H:%i:%s') - INTERVAL 30 MINUTE > CURRENT_TIME(),
                    1,
                    0
                ) AS `LOCK`
            FROM remun_medis.perjanjian_copy rmp 
            LEFT JOIN master.dokter mdok ON mdok.ID = rmp.ID_DOKTER
            LEFT JOIN master.pegawai mpeg ON mpeg.NIP = mdok.NIP AND mdok.`STATUS` != 0
            LEFT JOIN `master`.penjamin_ruangan pr ON mpeg.SMF = pr.RUANGAN_RS
            LEFT JOIN `master`.poli_bpjs_2 pb ON pr.RUANGAN_PENJAMIN = pb.kdsubspesialis
            LEFT JOIN remun_medis.jadwal rmj ON rmj.DOKTER = rmp.ID_DOKTER AND rmj.RUANGAN = rmp.ID_RUANGAN AND rmj.TANGGAL = rmp.TANGGAL AND rmj.`STATUS` != 0
            LEFT JOIN remun_medis.jadwal_detail jd ON rmp.SLOT = jd.ID_JADWAL
            LEFT JOIN `master`.ruangan mr ON rmp.ID_RUANGAN = mr.ID
            WHERE rmp.TANGGAL = DATE(NOW()) AND NOMR = ? AND rmp.STATUS != 0 AND rmp.DPJP != 0
            AND rmp.ID_RUANGAN NOT IN ('105020201')
            AND mr.GEDUNG IS NULL
            ORDER BY NOKONTROL ASC LIMIT 1";

        return $this->db->fetchOne($sql, [$nomr]);
    }

    /**
     * Get riwayat kunjungan terakhir
     */
    public function getLastVisit($nomr)
    {
        $sql = "SELECT p.NOMOR, p.TANGGAL, p.RUJUKAN, p.`STATUS` STATUS_PENDAFTRAN, r.DESKRIPSI, r.JENIS_KUNJUNGAN 
                FROM pendaftaran.pendaftaran p 
                LEFT JOIN pendaftaran.tujuan_pasien t ON p.NOMOR = t.NOPEN
                LEFT JOIN `master`.ruangan r ON t.RUANGAN = r.ID
                WHERE p.NORM = ? 
                AND p.RUJUKAN NOT IN('0','0904R008') AND p.`STATUS` != 0 AND t.`STATUS` != 0 AND r.JENIS_KUNJUNGAN NOT IN (2,3,4,11) 
                ORDER BY p.TANGGAL DESC LIMIT 1";

        return $this->db->fetchOne($sql, [$nomr]);
    }

    /**
     * Insert log APM helpdesk
     */
    public function insertLogApm($nomr, $tagihan = null)
    {
        $data = [
            'nomr' => $nomr,
            'tanggal' => date('Y-m-d H:i:s')
        ];

        if ($tagihan) {
            $data['tagihan'] = $tagihan;
        }

        return $this->db->insert('log.log_apmhelpdesk', $data);
    }

    /**
     * Validasi nomor MR
     */
    public function validateMr($nomr)
    {
        // Validasi format nomor MR (hanya angka)
        if (!preg_match('/^[0-9]+$/', $nomr)) {
            return false;
        }

        // Validasi panjang nomor MR
        if (strlen($nomr) < 1 || strlen($nomr) > 10) {
            return false;
        }

        return true;
    }
}
