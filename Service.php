<?php
/* kode error
	500: Error Query Select / Tidak Konek ke Database
	501: Peserta tidak terdaftar di table bpjs.peserta
*/
// ini_set('display_errors', '1');
// ini_set('display_startup_errors', '1');
// error_reporting(E_ALL);
date_default_timezone_set('Asia/Jakarta');
class Service {
	private $config;
	
	function __construct() {
		$this->init();
	}
	
	private function init() {
		$this->config = parse_ini_file("config.cnf", true);
		$this->config = $this->config["BPJServices"];
	}
	
	private function sendRequest($action = "", $method = "GET", $data = "", $contenType = "application/json", $url = "") {
    ob_start();
    // $out = fopen('php://output', 'w');
		$curl = curl_init();
    // curl_setopt($curl, CURLOPT_VERBOSE, true);
    // curl_setopt($curl, CURLOPT_STDERR, $out);
		$url = ($url == '' ? $this->config["url"].$action : $url);
		$headers = array();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_HEADER, false);
		$headers[count($headers)] = "Key: 2017RsdjServiceSimpel3212";
		$headers[count($headers)] = "Content-type: ".$contenType;
		$headers[count($headers)] = "Content-length: ".strlen($data);
		curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		
		//curl_setopt($curl, CURLOPT_FAILONERROR, true); 
		curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true); 
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); 
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); 
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); 
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    
		$result = curl_exec($curl);
    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    
    curl_close($curl);
    // fclose($out);
    // $data = ob_get_clean();
    // $data .= PHP_EOL . $result . PHP_EOL;
    $result = json_decode($result);
    if($http_status !== 200) {
      $result = array(
        'success' => false,
        'code' => $http_status,
        'message' => $result->detail
      );
    }

		// $result = json_decode($result);
		// if($result) {
		// 	if($result->data) $result = json_encode($result);
		// }
		return $result;
	}
	
	/* Peserta>>>No.Kartu BPJS
	 * cari peserta berdasarkan Nomor Kartu BPJS 
	 */
	function cariPesertaDgnNoKartuBPJS($nomor, $tglSep) {
		$tgl = isset($tglSep) ? $tglSep : date("Y-m-d");
		return $this->sendRequest("webservice/plugins/bpjs/".$nomor."?tglSEP=".$tgl);
	}

  /* Rujukan>>>Data Rujukan Berdasarkan Nomor kartu
	 * cari data rujukan berdasarkan Nomor Kartu BPJS 
	 */
	function cariRujukanDgnNoKartuBPJS($nomor) {
		return $this->sendRequest("webservice/plugins/bpjs/cariRujukanDgnNoKartuBPJS/".$nomor);
	}

  /* SEP>>>Insert SEP
	 * generate Nomor SEP
	 */
	function generateNoSEP($noKartu, $tglSep, $asalRujukan = "2", $tglRujukan, $noRujukan, $ppkRujukan, 
  $jnsPelayanan, $catatan = "-", $diagAwal, $poliTujuan, $eksekutif, $cob = "0", $noTelp = "", $user, $noMr, $create, $ip, $lakaLantas = "2", $lokasiLaka = "-", $penjamin = "", $noKontrol, $dpjp, $kelasRawat
  ) {
    $params = array(
      "noKartu" => $noKartu
      , "tglSep" => $tglSep
      , "jnsPelayanan" => $jnsPelayanan
      , "klsRawat" => $kelasRawat
      , "norm" => $noMr
      
      // rujukan
      , "asalRujukan" => $asalRujukan // 1 = Faskes 1, 2 = Faskes 2 (RS)	#ADDED
      , "tglRujukan" => $tglRujukan
      , "noRujukan" => $noRujukan
      , "ppkRujukan" => $ppkRujukan
      
      , "catatan" => $catatan
      , "diagAwal" => $diagAwal
      
      // poli
      , "poliTujuan" => $poliTujuan
      , "ekseKutif" => $eksekutif // 0 = Tidak, 1 = Ya				#ADDED
      
      , "cob" => $cob // 0 = Tidak, 1 = Ya						#ADDED
        
      , "katarak" => 0

      , "tujuanKunj" => '0'
      , "flagProcedure" => ''
      , "kdPenunjang" => ''
      , "assesmentPel" => '4'
      // jaminan
      , "lakaLantas" => $lakaLantas // 2 = Tidak, 1 = Ya # Skrg 0 = Tidak, 1 = Ya
      , "penjamin" => $penjamin // 1=Jasa raharja PT, 2=BPJS Ketenagakerjaan, 3=TASPEN PT, 4=ASABRI PT} jika lebih dari 1 isi -> 1,2 (pakai delimiter koma)	#ADDED
      , "lokasiLaka" => $lokasiLaka
      //sdkp
      , "noKontrol" => $noKontrol
      , "dpjp" => $dpjp
      , "noKontak" => $noTelp
      , "user" => $user
    );
    // return $params;
    return $this->sendRequest("webservice/plugins/bpjs", "POST", json_encode($params));
  }

  function pendaftaran($params){
    return $this->sendRequest("webservice/pendaftaran/kios/buatRegistrasiKios", "POST", $params);
  }

  function rencanaKontrol($params){
    return $this->sendRequest("webservice/plugins/bpjs/rencanaKontrol", "POST", $params);
  }
}
?>
