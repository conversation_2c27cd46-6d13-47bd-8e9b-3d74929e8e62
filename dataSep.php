<!DOCTYPE html>
<html>
<link rel="stylesheet" href="css/sweetalert.min.css">
<script src="js/sweetalert.min.js"></script>
<script src="js/jquery-2.1.4.min.js"></script>
<script src="js/modernizr.js"></script>
<script src="assets/printdoc.js"></script>

<head>
<style type="text/css">
	.no-js #loader { display: none;  }
	.js #loader { display: block; position: absolute; left: 100px; top: 0; }
	.se-pre-con {
		position: fixed;
		left: 0px;
		top: 0px;
		width: 100%;
		height: 100%;
		z-index: 9999;
		background: url(images/loader-128x/Preloader_3.gif) center no-repeat #fff;
	}
</style>

</head>
<body>
<!-- <div class="se-pre-con"></div> -->
</body>
</html>
<?php

include "Service.php";
include "koneksi.php";
include "BpjsService.php";

$sep = $_POST['sep'];
// echo $sep;
// die();
if($sep == 1){
	$bpjsService = new BpjsService();

	$tglSep = $_POST['tglRencanaKontrol'];
	$nomr = $_POST['nomr'];
	$noKartu = $_POST['nobpjs'];
	$noRujukan = $_POST['rujukan'];
	$tglRujukan = $_POST['tglRujukan'];
	$tglRencanaKontrol = $_POST['tglRencanaKontrol'];
	$diagAwal = $_POST['diagawal'];
	$jnsPelayanan = 2;
	$ppkRujukan  = $_POST['ppkrujukan'];
	$smf = $_POST['smf'];
	$ruangan = $_POST['idruangan'];
	$dokter = $_POST['idDokter'];
	$noTelp = $_POST['notelp'];
	$dpjpsimpel = $_POST['idhafis'];
	$statusKunjungan = $_POST['statusKunjungan'];
	$namaPasien = $_POST['pasien'];
	$namaDokter = $_POST['namaDokter'];
	/* if($query_layanan){
		$jenisRuangSebelum = $row_layanan['jenisPelayanan'];
		# BPJS 1:Ranap, 2:Rajal
		if($jenisRuangSebelum == 1){
			// $dpjphafis = $_POST['hafisSK'];
			// $noRujukan = $noSEPSebelum;
			// $ppkRujukan = '0904R008';
		}else{
			$poliSebelum = $row_layanan['PTJ'];
			$noRujukan = $_POST['rujukan'];
			// echo die($poliSebelum);
			
			if(	$poliSebelum == 'ANA'||$poliSebelum == 'INT'||$poliSebelum == 'MAT'||$poliSebelum == 'GIG'||$poliSebelum == '045'||$poliSebelum == 'THT'||$poliSebelum == 'ORT'||$poliSebelum == 'BSY'||$poliSebelum == 'JAN'||$poliSebelum == 'BDP'||$poliSebelum == 'URO'||$poliSebelum == '018'||$poliSebelum == 'BTK'||$poliSebelum == 'BED'||$poliSebelum == 'PAR'||$poliSebelum == 'IRM'||$poliSebelum == 'KDN'||$poliSebelum == '005'||$poliSebelum == '004'||$poliSebelum == '021'||$poliSebelum == '030'||$poliSebelum == '017'||$poliSebelum == 'OBG'
			||$poliSebelum == 'RAT'||$poliSebelum == 'HDL'||$poliSebelum == 'SAR'||$poliSebelum == '008'||$poliSebelum == 'KEM')
			{
				//ada default
				// $sql_default="SELECT IDHAFIS FROM master.mapping_dokter_bpjs WHERE POLIBPJS='$poliSebelum'";
				// $query_default = mysqli_query($conn, $sql_default);

				// $row_hafis_default = mysqli_fetch_assoc($query_default);
				// $hafisDefault = $row_hafis_default['IDHAFIS'];

				// $dpjphafis = $hafisDefault;
				// echo die($dpjphafis);
				
			}elseif($poliSebelum == 'IGD'){
				$sql_igd = "SELECT bpkj.jenisPelayanan, bpkj.noSEP noSEPRanap, bpkj.dpjp, bpkj.poliTujuan PTJ
						FROM bpjs.peserta bps 
						LEFT JOIN bpjs.kunjungan bpkj ON bps.noKartu=bpkj.noKartu

						WHERE bpkj.noKartu='$noKartu' AND bpkj.errMsgBatalSEP IS NULL AND bpkj.poliTujuan != 'IGD'
						ORDER BY bpkj.tglSEP DESC LIMIT 1";
				$query_igd = mysqli_query($conn, $sql_igd);
				$row_igd = mysqli_fetch_assoc($query_igd);
				if($query_igd){
					$dpjphafis = $row_layanan['dpjp'];
				}
			}
			else{
				$dpjphafis = $_POST['idhafis'];
			}		
		}
	} */

	// echo die($dpjphafis);

	/* if($dpjphafis == 0){
		// $sql_dpjp = "SELECT dpjp FROM bpjs.kunjungan WHERE noKartu = '$noKartu' AND jenisPelayanan=2 AND poliTujuan != 'IGD' AND dpjp != 0 ORDER BY DATE(tglSEP) DESC";
		$sql_dpjp = "SELECT pp.NOMOR, tp.DOKTER, md.HAFIS FROM pendaftaran.pendaftaran pp
				LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN=pp.NOMOR
				LEFT JOIN master.ruangan mr ON mr.ID = tp.RUANGAN
				LEFT JOIN `master`.dokter md ON md.ID = tp.DOKTER
				WHERE pp.NORM='$nomr' AND pp.`STATUS` != 0 AND mr.JENIS = 5 
				AND mr.JENIS_KUNJUNGAN NOT IN (2,3)
				ORDER BY NOMOR DESC LIMIT 1";
		$query_dpjp = mysqli_query($conn, $sql_dpjp);
		if($query_dpjp){
			$row_dpjp = mysqli_fetch_assoc($query_dpjp);
			$dpjphafis = $row_dpjp['HAFIS'];
		}else{
			echo $sql_dpjp;
		}
	}else{
		$dpjphafis = $dpjphafis;
	} */

	$service = new Service();

	$poliTujuan = $_POST['poliTujuan'];
	$namaPoliTujuan = $_POST['namaPoliTujuan'];
	// $sql_layanan = "SELECT bpkj.jenisPelayanan, bpkj.noSEP noSEPRanap, bpkj.dpjp, bpkj.poliTujuan PTJ
	// 		FROM bpjs.peserta bps 
	// 		LEFT JOIN bpjs.kunjungan bpkj ON bps.noKartu=bpkj.noKartu

	// 		WHERE bpkj.noKartu='$noKartu' AND bpkj.poliTujuan = '$poliTujuan' AND jenisPelayanan=2 AND bpkj.errMsgBatalSEP IS NULL 
	// 		ORDER BY bpkj.tglSEP DESC LIMIT 1";
	// $query_layanan = mysqli_query($conn, $sql_layanan);
	// $row_layanan = mysqli_fetch_assoc($query_layanan);
	// $noSEPSebelum=$row_layanan['noSEPRanap'];

	// var_dump($poliTujuan);die();

	//if PoliTujuan Kosong
	if(empty($poliTujuan) || $poliTujuan == '') {
		//$sql_poli = "SELECT RUANGAN_PENJAMIN FROM master.penjamin_ruangan WHERE STATUS=1 AND RUANGAN_RS = $smf";
		$sql_poli="SELECT md.ID,md.HAFIS, mpeg.NIP, mpeg.SMF, mf.DESKRIPSI, mj.RUANGAN_PENJAMIN FROM master.dokter md 
			LEFT JOIN master.pegawai mpeg ON mpeg.NIP=md.NIP
			LEFT JOIN master.referensi mf On mf.ID = mpeg.SMF AND mf.JENIS=26
			LEFT JOIN master.penjamin_ruangan mj ON mj.RUANGAN_RS=mpeg.SMF
			WHERE md.HAFIS = '$dpjphafis'";
		$query_poli = mysqli_query($conn,$sql_poli);
		if($query_poli){
			$row_poli = mysqli_fetch_assoc($query_poli);
			$poliTujuan = $row_poli['RUANGAN_PENJAMIN'];

			var_dump($poliTujuan);
			var_dump($poliSebelum);
		}else{
			// echo $sql_poli;
		}
	}else{
		$poliTujuan = $_POST['poliTujuan'];
	}

	/*cek pendaftaran simpel terakhir*/
	$sql_poli="SELECT mr.JENIS_KUNJUNGAN, tp.RUANGAN, pj.NOMOR sepri
		FROM pendaftaran.pendaftaran pp
		LEFT JOIN pendaftaran.tujuan_pasien tp ON tp.NOPEN = pp.NOMOR
		LEFT JOIN master.ruangan mr ON mr.ID = tp.RUANGAN AND mr.JENIS=5
		LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = pp.NOMOR
		WHERE pp.NORM = '$nomr' AND pp.STATUS != 0 AND mr.JENIS_KUNJUNGAN != 2 ORDER BY pp.TANGGAL DESC LIMIT 1";
	// echo $sql_poli;
	// exit();

	$query_poli = mysqli_query($conn,$sql_poli);
	if($query_poli){
		$row_poli = mysqli_fetch_assoc($query_poli);
		$jenisPoliTujuan = $row_poli['JENIS_KUNJUNGAN'];

		if($jenisPoliTujuan == 3){
			// $dpjphafis = $_POST['hafisSK'];
			// $noRujukan = $row_poli['sepri'];
			// $ppkRujukan = '0904R008';
			// $poliTujuan = $_POST['poliTujuan'];
			//var_dump($noRujukan);
			//exit();
		}
	}

	if (strpos($ppkRujukan, 'R') !== false) {
		$asalRujukan = "2";
	}else{
		$asalRujukan = "1";

		//$noRujukan = '0904R008';
		//$ppkRujukan = '0904R008';
		//$poliTujuan = 'IGD';
	}

	$kelas = $_POST['kelasRawat'];
	$ekseKutif = strval(0);
	$user = strval(1065);
	$ip = "*************";
	$create = 0;

	// get dokter dpjp 
	$dpjphafis = $dpjpsimpel;
	// if($poliTujuan != '017'){
		$resultDokter = $bpjsService->request('vclaimv2/rencanaKontrol/jadwalDokter', ['jnsKontrol' => 2, 'kdPoli' => $poliTujuan, 'tglRencanaKontrol' => date("Y-m-d")], 'GET');
		if($resultDokter->metaData->code == 200){
			$dpjphafis=  $resultDokter->response->list[0]->kodeDokter;
			$namaDokter=  $resultDokter->response->list[0]->namaDokter;
		}
	// }
	if($_POST['namaDokter'] == "")
	{
	?>
		<script type='text/javascript'>
			var status = "<?php echo $statusPeserta; ?>";
			document.addEventListener("DOMContentLoaded", function(event) {
				swal({
						icon : 'warning',
						title: "Silakan Pilih Nama Dokter",
						timer: 5000,
					}).then(function() {
					window.location = "index.php";
				});
			});
		</script>
	<?php
	}else{
		$poliRujukan = $_POST['poliRujukan'];
		$noKontrol = "";
		$kodeDPJP = "";
		$tujuanKunj = "0";
		$assesmentPel = "";
		// echo $dpjphafis.'-'.$noSEPSebelum.'-'.$noRujukan.'<br/>';
		// echo $poliTujuan.'-'.$poliRujukan.'-'.$statusKunjungan;die();
		// statusKunjungan 1 rujukan pertama 
		if($statusKunjungan == 0){
			if($poliTujuan == $poliRujukan){

				$tglMulai = date('Y-m-d', strtotime('- 90 days'));
				$monitoring = $bpjsService->request('vclaimv2/monitoring/historyPeserta', ['noKartu' => $noKartu, 'tglMulai' => $tglMulai, 'tglAkhir' => date("Y-m-d")], 'GET');
				$resultMonitoring = $monitoring->response->histori;

				$dataMonitoring=array();
				$a=0;

				function like_match($pattern, $subject)
				{
						$pattern = str_replace('%', '.*', preg_quote($pattern, '/'));
						return (bool) preg_match("/^{$pattern}$/i", $subject);
				}

				foreach ($resultMonitoring as $index => $json) {
					if ($json->ppkPelayanan == 'RS KANKER DHARMAIS' && like_match("$noRujukan%", $json->noRujukan) && like_match("$namaPoliTujuan%", $json->poli)) {
						while ($a < 1) {
							$dataMonitoring = $resultMonitoring[$index];
							$a++;
						}
					}
				}
				$noSEPSebelum = $dataMonitoring->noSep;
				// echo json_encode($dataMonitoring);die();
				// $dataRencanaKontrol = '{
				// 	"kodeDokter":"'.$dpjphafis.'",
				// 	"noSEP":"'.$noSEPSebelum.'",
				// 	"poliKontrol":"'.$poliTujuan.'",
				// 	"tglRencanaKontrol":"2022-01-29",
				// 	"user":"1065"
				// }';
				// echo json_encode($dataRencanaKontrol);die();
				// $poliTujuan = $poliSebelum;
				$tujuanKunj = "2";
				$assesmentPel = "1";
				$dataRencanaKontrol = array(
					"noSEP" => $noSEPSebelum,
					"kodeDokter" => $dpjphafis,
					"poliKontrol" => $poliTujuan,
					"tglRencanaKontrol" => $tglRencanaKontrol,
					"user" => "1065"
				);

				// echo json_encode($dataRencanaKontrol);die();
				// echo json_encode($bpjsService->request('vclaimv2/rencanaKontrol/insert', $dataRencanaKontrol, 'POST'));die();
				
				// 	$rencanaKontrol = $service->rencanaKontrol($dataRencanaKontrol);

				/*cek surat kontrol*/
				$sql_suratkontrol="SELECT * FROM bpjs.suratkontrol s WHERE s.noKartu='$noKartu' AND s.tglRencanaKontrol='$tglRencanaKontrol' AND s.status=1";

				$query_suratkontrol = mysqli_query($conn,$sql_suratkontrol);
				if(mysqli_num_rows($query_suratkontrol)){
					$row_suratkontrol = mysqli_fetch_assoc($query_suratkontrol);
					$noKontrol = $row_suratkontrol['noSuratKontrol'];
					$kodeDPJP = $row_suratkontrol['kodeDokter'];
					$dpjphafis= $row_suratkontrol['kodeDokter'];

				} else {
					$surkon = $bpjsService->request('vclaimv2/rencanaKontrol/list', ['noKartu' => $noKartu, 'tahun' => date("Y"), 'bulan' => date("m")], 'GET');
					$resultSurkon = $surkon->response->list;
					$dataSurkon=array();
					$a=0;

					foreach ($resultSurkon as $index => $json) {
						if ($json->tglRencanaKontrol == $tglRencanaKontrol) {
							while ($a < 1) {
								$dataSurkon = $resultSurkon[$index];
								$a++;
							}
						}
					}
					
					if($dataSurkon == NULL) {
						$rencanaKontrol = $bpjsService->request('vclaimv2/rencanaKontrol/insert', $dataRencanaKontrol, 'POST');
			
						if($rencanaKontrol->metaData->code == 200){
							$noKontrol = $rencanaKontrol->response->noSuratKontrol;
							$kodeDPJP = $dpjphafis;
							$sql_logNoKontrol = "INSERT INTO bpjs.suratkontrol(noSEP, kodeDokter, poliKontrol, noSuratKontrol, noKartu, namaDokter, 
							tglRencanaKontrol, namaPasien, user) VALUES ('$noSEPSebelum', '$dpjphafis', '$poliTujuan', '$noKontrol', '$noKartu', '$namaDokter', '$tglRencanaKontrol', '$namaPasien', '$user')";
							// die($sql_logNoKontrol);
							$query_logNoKontrol = mysqli_query($conn, $sql_logNoKontrol) OR die(mysqli_error($conn));
						}else{
							// echo 'no kontrol: '.json_encode($rencanaKontrol);
							// echo json_encode($dataRencanaKontrol);
							// die();
							$messageBPJS = $rencanaKontrol->metaData->message;
							$kodeBPJS = $rencanaKontrol->metaData->code;
							$sql_logBPJS = "INSERT INTO `log`.`log_apmBPJS`(`NOMR`, `MESSAGE`, `KODE`, `TANGGAL`, `NOKARTU`, `TGLSEP`, 
							`ASALRUJUKAN`, `TGLRUJUKAN`, `NORUJUKAN`, `PPKRUJUKAN`, `JENIS_PELAYANAN`, `CATATAN`, `DIAG_AWAL`, 
							`POLI_TUJUAN`, `EKSEKUTIF`, `COB`, `NOTELP`, `USER`, `CREATE`, `IP`, `LOKASILAKA`, `PENJAMIN`, 
							`NOKONTROL`, `HAFIS`) VALUES ('$nomr', '$messageBPJS ', '$kodeBPJS', NOW(), '$noKartu', '$tglSep', '$asalRujukan', '$tglRujukan', '$noRujukan', '$ppkRujukan', 2, '-', '$diagAwal', '$poliTujuan', '$ekseKutif', 0, '$noTelp', '$user', '$create', '$ip', 0, '-', '$noKontrol', '$dpjphafis')";
							$query_logBPJS = mysqli_query($conn, $sql_logBPJS);
							?>
								<script type='text/javascript'>
									document.addEventListener("DOMContentLoaded", function(event) {
										swal({
											//text: '<?php echo $resultSep->metaData->message; ?>',
											icon : 'error',
											title : 'Maaf!',
											text: '<?php echo "Error BPJS, Silakan Ambil Nomor Antrian."; ?>',
											timer: 8000,
											button : false
										}).then(function() {
											window.location = "index.php";
										});
									});
								</script>
							<?php 
						}
					}else {
						$noKontrol = $dataSurkon->noSuratKontrol;
						$kodeDPJP = $dataSurkon->kodeDokter;
					}
				}
			}else{
				$assesmentPel = "1";
			}
		}
		// die();
		// $resultSep = $service->generateNoSEP($noKartu, $tglSep, $asalRujukan, $tglRujukan, $noRujukan, $ppkRujukan, $jnsPelayanan = 2, $catatan = "-", $diagAwal, $poliTujuan, $ekseKutif, $cob = "0", $noTelp, $user, $nomr, $create, $ip, $lakaLantas = "0", $lokasiLaka = "-", $penjamin = "", $noKontrol, $dpjphafis, $kelas);
		// for ($x = 0; $x <= 4; $x++) {
		// 	$dataSep['assesmentPel'] = $x;
		// 	if($resultSep->metaData->code == 200){
		// 		break;
		// 	}	
		// }

		// $j = [0,2];
	// $i = [1,2,3,4,5];

	// foreach ($i as $valuei) {
	//   $assesmentPel = $valuei;
	//   foreach ($j as $valuej) {
	//     $tujuanKunj = $valuej;
		// 		$dataSep = array(
		// 			"noKartu" => $noKartu,
		// 			"tglSep" => $tglSep,
		// 			"ppkPelayanan" => "0904R008",
		// 			"jnsPelayanan" => "2",
		// 			"klsRawatHak" => $kelas,
		// 			"klsRawatNaik" => "",
		// 			"pembiayaan" => "",
		// 			"penanggungJawab" => "",
		// 			"noMR" => $nomr,
		// 			"asalRujukan" => $asalRujukan,
		// 			"tglRujukan" => $tglRujukan,
		// 			"noRujukan" => $noRujukan,
		// 			"ppkRujukan" => $ppkRujukan,
		// 			"catatan" => "-",
		// 			"diagAwal" => $diagAwal,
		// 			"tujuan" => $poliTujuan,
		// 			"eksekutif" => "0",
		// 			"cob" => "0",
		// 			"katarak" => "0",
		// 			"lakaLantas" => "0",
		// 			"noLP" => "",
		// 			"tglKejadian" => "",
		// 			"keterangan" => "",
		// 			"suplesi" => "0",
		// 			"noSepSuplesi" => "",
		// 			"kdPropinsi" => "",
		// 			"kdKabupaten" => "",
		// 			"kdKecamatan" => "",
		// 			"tujuanKunj" => $tujuanKunj,
		// 			"flagProcedure" => "",
		// 			"kdPenunjang" => "",
		// 			"assesmentPel" => $assesmentPel,
		// 			"noSurat" => $noKontrol,
		// 			"kodeDPJP" => $kodeDPJP,
		// 			"dpjpLayan" => $dpjphafis,
		// 			"noTelp" => $noTelp,
		// 			"user" => "1065",
		// 			"ip" => $ip
		// 		);
		// 		$resultSep = $bpjsService->request('vclaimv2/sep/insert', $dataSep, 'POST');

	//     if($resultSep->metaData->code == 200){
	//       break;
	//     }
	//   }
	//   if($resultSep->metaData->code == 200){
	//     break;
	//   }
		// }

		$dataSep = array(
			"noKartu" => $noKartu,
			"tglSep" => $tglSep,
			"ppkPelayanan" => "0904R008",
			"jnsPelayanan" => "2",
			"klsRawatHak" => $kelas,
			"klsRawatNaik" => "",
			"pembiayaan" => "",
			"penanggungJawab" => "",
			"noMR" => $nomr,
			"asalRujukan" => $asalRujukan,
			"tglRujukan" => $tglRujukan,
			"noRujukan" => $noRujukan,
			"ppkRujukan" => $ppkRujukan,
			"catatan" => "-",
			"diagAwal" => $diagAwal,
			"tujuan" => $poliTujuan,
			"eksekutif" => "0",
			"cob" => "0",
			"katarak" => "0",
			"lakaLantas" => "0",
			"noLP" => "",
			"tglKejadian" => "",
			"keterangan" => "",
			"suplesi" => "0",
			"noSepSuplesi" => "",
			"kdPropinsi" => "",
			"kdKabupaten" => "",
			"kdKecamatan" => "",
			"tujuanKunj" => $tujuanKunj,
			"flagProcedure" => "",
			"kdPenunjang" => "",
			"assesmentPel" => $assesmentPel,
			"noSurat" => $noKontrol,
			"kodeDPJP" => $kodeDPJP,
			"dpjpLayan" => $dpjphafis,
			"noTelp" => $noTelp,
			"user" => "1065",
			"ip" => $ip
		);
		$resultSep = $bpjsService->request('vclaimv2/sep/insert', $dataSep, 'POST');

		// echo json_encode($resultSep).'<br/>';
		// echo json_encode($dataSep).'<br/>';
		// echo $resultSep->metaData->code;
		// die();
		$status =  $resultSep->metaData->code;
		
		if($status == 200){
			$noSep = $resultSep->response->sep->noSep;
			$tglSep = $resultSep->response->sep->tglSep;
			
			$messageBPJS = $status;
			$kodeBPJS = $resultSep->metaData->code;

			$sql_logBPJS = "INSERT INTO `log`.`log_apmBPJS`(`NOMR`, `MESSAGE`, `KODE`, `TANGGAL`, `NOKARTU`, `TGLSEP`, `ASALRUJUKAN`, `TGLRUJUKAN`, `NORUJUKAN`, `PPKRUJUKAN`, `JENIS_PELAYANAN`, `CATATAN`, `DIAG_AWAL`, `POLI_TUJUAN`, `EKSEKUTIF`, `COB`, `NOTELP`, `USER`, `CREATE`, `IP`, `LOKASILAKA`, `PENJAMIN`, `NOKONTROL`, `HAFIS`) VALUES ('$nomr', '$messageBPJS ', '$kodeBPJS', NOW(), '$noKartu', '$tglSep', '$asalRujukan', '$tglRujukan', '$noRujukan', '$ppkRujukan', 2, '-', '$diagAwal', '$poliTujuan', '$ekseKutif', 0, '$noTelp', '$user', '$create', '$ip', 0, '-', '$noKontrol', '$dpjphafis')";
			
			$query_logBPJS = mysqli_query($conn, $sql_logBPJS);
			if($kelas == 1){
				$kelasSIMPEL = 3;
			}elseif($kelas == 3){
				$kelasSIMPEL = 1;
			}else{
				$kelasSIMPEL = 2;
			}
			$daftar = '{
				"NORM":"'.$nomr.'",
				"NO_SEP":"'.$noSep.'",
				"TGL_SEP":"'.$tglSep.'",
				"JENIS_LAYANAN":"2",
				"CATATAN":"-",
				"TANGGAL_RUJUKAN":"'.$tglRujukan.'",
				"DIAGNOSA":"'.$diagAwal.'",
				"PPK_RUJUKAN":"'.$ppkRujukan.'",
				"NOMOR_RUJUKAN":"'.$noRujukan.'",
				"PAKET":"0",
				"OLEH":"1065",
				"JENIS_PENJAMIN":"2",
				"NOMOR_KARTU":"'.$noKartu.'",
				"KELAS_SIMPEL":"'.$kelasSIMPEL.'",
				"KELAS_BPJS":"'.$kelas.'",
				"RUANGAN":"'.$ruangan.'",
				"RESERVASI":"",
				"SMF":"'.$smf.'",
				"DOKTER":"'.$dokter.'",
				"KODE_POLI":"'.$poliTujuan.'",
				"NO_KONTROL":"'.$noKontrol.'",
				"DPJP":"'.$dpjpsimpel.'"
			}';

			$result = $service->pendaftaran($daftar);
			// echo json_encode($result,JSON_PRETTY_PRINT);die();
			
			$sql_cek = "SELECT * FROM pendaftaran.pendaftaran WHERE NORM='$nomr' AND STATUS=1";
			$query_check = mysqli_query($conn, $sql_cek);
			if($query_check){
				$count = mysqli_num_rows($query_check);
				if($count > 0){ ?>
					<script type="text/javascript">
					var sep = '<?php echo $noSep; ?>';
					var cetak = "bpjs.CetakSEP_1";
					var poli = '<?=$poliTujuan?>';
					var ruangan = '<?=$ruangan?>';

					// if(poli=='THT') {
					// 	cetak="bpjs.CetakSEP_1";
					// }
					if(ruangan != '105120101') {
						window.requestPrint({
							NAME:cetak,
							TYPE:"Word",
							EXT:"docx",
							PARAMETER:{
								PSEP:sep,CETAK_HEADER:1
							},
							REQUEST_FOR_PRINT:true,
							PRINT_NAME:"CetakSEP",
							CONNECTION_NUMBER:0,
							COPIES:1,
							id:"data.model.RequestReport-1"
						});

						document.addEventListener("DOMContentLoaded", function(event) {
							swal({
								icon : 'success',
								title : 'Sukses!',
								text: 'Mohon cek kembali kesesuaian data di SEP. Segera ke helpdesk jika ada kesalahan.',
								timer: 8000,
								button : false
							}).then(function() {
								window.location = "index.php";
							});
						});
						$(window).load(function() {
							$(".se-pre-con").fadeOut("slow");
						});
					} else {
						document.addEventListener("DOMContentLoaded", function(event) {
							swal({
								icon : 'success',
								title : 'Sukses!',
								text: 'Kiosk sudah tidak mencetak Bukti Registrasi, anda dapat melihatnya melalui RSKD Mobile. Segera ke helpdesk jika ada kesalahan.',
								timer: 8000,
								button : false
							}).then(function() {
								window.location = "index.php";
							});
						});
						$(window).load(function() {
							$(".se-pre-con").fadeOut("slow");
						});
					}
					</script>
		<?php
				}else{
					$message = json_encode(['request' => $daftar,'response' => $result]);
					$sql_logBPJS = "INSERT INTO `log`.`log_apmBPJS`(`NOMR`, `MESSAGE`, `KODE`, `TANGGAL`, `NOKARTU`, `TGLSEP`, `ASALRUJUKAN`, `TGLRUJUKAN`, `NORUJUKAN`, `PPKRUJUKAN`, `JENIS_PELAYANAN`, `CATATAN`, `DIAG_AWAL`, `POLI_TUJUAN`, `EKSEKUTIF`, `COB`, `NOTELP`, `USER`, `CREATE`, `IP`, `LOKASILAKA`, `PENJAMIN`, `NOKONTROL`, `HAFIS`) VALUES ('$nomr', 'SIMPEL-$message', '201', NOW(), '$noKartu', '$tglSep', '$asalRujukan', '$tglRujukan', '$noRujukan', '$ppkRujukan', 2, '-', '$diagAwal', '$poliTujuan', '$ekseKutif', 0, '$noTelp', '$user', '$create', '$ip', 0, '-', '$noKontrol', '$dpjphafis')";
			
					$query_logBPJS = mysqli_query($conn, $sql_logBPJS);
		?>
			<script type='text/javascript'>
				document.addEventListener("DOMContentLoaded", function(event) {
						swal({
							icon : 'error',
							title : 'Maaf!',
							text: '<?php echo "Error BPJS, Silakan Ambil Nomor Antrian."; ?>',
							timer: 8000,
							button : false
						}).then(function() {
							window.location = "index.php";
						});
				});
				</script>
		<?php
				}
			}
		} else { 
			// echo 'SEP :'. json_encode($resultSep);
			// echo json_encode($dataSep);
			// die();

			$messageBPJS = $resultSep->metaData->message;
			$kodeBPJS = $resultSep->metaData->code;
			$sql_logBPJS = "INSERT INTO `log`.`log_apmBPJS`(`NOMR`, `MESSAGE`, `KODE`, `TANGGAL`, `NOKARTU`, `TGLSEP`, 
			`ASALRUJUKAN`, `TGLRUJUKAN`, `NORUJUKAN`, `PPKRUJUKAN`, `JENIS_PELAYANAN`, `CATATAN`, `DIAG_AWAL`, 
			`POLI_TUJUAN`, `EKSEKUTIF`, `COB`, `NOTELP`, `USER`, `CREATE`, `IP`, `LOKASILAKA`, `PENJAMIN`, 
			`NOKONTROL`, `HAFIS`) VALUES ('$nomr', '$messageBPJS ', '$kodeBPJS', NOW(), '$noKartu', '$tglSep', '$asalRujukan', '$tglRujukan', '$noRujukan', '$ppkRujukan', 2, '-', '$diagAwal', '$poliTujuan', '$ekseKutif', 0, '$noTelp', '$user', '$create', '$ip', 0, '-', '$noKontrol', '$dpjphafis')";
			$query_logBPJS = mysqli_query($conn, $sql_logBPJS);
		?>
				<script type='text/javascript'>
				document.addEventListener("DOMContentLoaded", function(event) {
						swal({
							//text: '<?php echo $resultSep->metaData->message; ?>',
							icon : 'error',
							title : 'Maaf!',
							text: '<?php echo "Error BPJS, Silakan Ambil Nomor Antrian."; ?>',
							timer: 8000,
							button : false
						}).then(function() {
							window.location = "index.php";
						});
				});
				</script>
			<?php
		}
	}
} else {
	$sql_cek = "SELECT * FROM pendaftaran.pendaftaran WHERE NORM='$nomr' AND STATUS=1";
	$query_check = mysqli_query($conn, $sql_cek);
	if($query_check){
		$count = mysqli_num_rows($query_check);
		if($count > 0){ ?>
			<script type='text/javascript'>
				document.addEventListener("DOMContentLoaded", function(event) {
				swal({
						icon : 'info',
						title : 'Informasi!',
						text: 'Sudah Terdaftar.',
						timer: 8000,
						button : false
					}).then(function() {
						window.location = "index.php";
					});
				});
				// Wait for window load
				$(window).load(function() {
					// Animate loader off screen
					$(".se-pre-con").fadeOut("slow");
				});
			</script>
		<?php 
		}
	} 

	$service = new Service();

	$oleh = 1065;
	$status = 1;
	$jenisJaminan = 2;
	$diagnosaDefault = 40931;
	$tglSep = $_POST['tglRencanaKontrol'];
	$nomr = $_POST['nomr'];
	$noKartu = $_POST['nobpjs'];
	$noRujukan = $_POST['rujukan'];
	$tglRujukan = $_POST['tglRujukan'];
	$tglRencanaKontrol = $_POST['tglRencanaKontrol'];
	$diagAwal = $_POST['diagawal'];
	$jnsPelayanan = 2;
	$ppkRujukan  = $_POST['ppkrujukan'];
	$smf = $_POST['smf'];
	$ruangan = $_POST['idruangan'];
	$dokter = $_POST['idDokter'];
	$noTelp = $_POST['notelp'];
	$dpjpsimpel = $_POST['idhafis'];
	$statusKunjungan = $_POST['statusKunjungan'];
	$namaPasien = $_POST['pasien'];
	$namaDokter = $_POST['namaDokter'];
	$kelas = $_POST['kelasRawat'];
	$kodeSEP = 'PK';
	if($error == 1){
		$kodeSEP = 'SM';
	}
	$noSep = date('Ymd').str_pad($nomr, 8, '0', STR_PAD_LEFT).$kodeSEP;
	if($kelas == 1){
		$kelasSIMPEL = 3;
	}elseif($kelas == 3){
		$kelasSIMPEL = 1;
	}else{
		$kelasSIMPEL = 2;
	}

	try {
		// Mulai transaksi
		$mysqli->begin_transaction();
		$tanggal = date('Y-m-d');
		$waktu = date('Y-m-d H-i-s');

		// 1. Panggil function MySQL untuk mendapatkan NOPEN
		$sql = "SELECT generator.generateNoPendaftaran('$tanggal') AS NOPEN";
		$result = $mysqli->query($sql);
		
		// Cek jika query error
		if (!$result) {
			throw new Exception("Error saat menjalankan query: " . $mysqli->error . "\nSQL: " . $sql);
		}
		$row = $result->fetch_assoc();
		$nopen = $row['NOPEN'];

		// 2. Insert ke tabel pendaftaran
		$stmt1 = $mysqli->prepare("INSERT INTO pendaftaran.pendaftaran (NOMOR,NORM,TANGGAL,DIAGNOSA_MASUK,RUJUKAN,OLEH,STATUS) VALUES (?, ?, ?, ?, ?, ?, ?)");
		if (!$stmt1) {
			throw new Exception("Prepare statement gagal (pendaftaran): " . $mysqli->error);
		}
		$stmt1->bind_param('sisisii', $nopen, $nomr, $waktu, $diagnosaDefault, $noRujukan, $oleh, $status);
		$stmt1->execute();

		if ($stmt1->error) {
			throw new Exception("Execute statement gagal (pendaftaran): " . $stmt1->error);
		}

		// 3. Insert ke tabel tujuan pasien
		$stmt2 = $mysqli->prepare("INSERT INTO pendaftaran.tujuan_pasien (NOPEN, RUANGAN, SMF, DOKTER, STATUS) VALUES (?, ?, ?, ?, ?)");
		if (!$stmt2) {
			throw new Exception("Prepare statement gagal (tujuan pasien): " . $mysqli->error);
		}
		$stmt2->bind_param('ssiii', $nopen, $ruangan, $smf, $dokter, $status);
		$stmt2->execute();

		if ($stmt2->error) {
			throw new Exception("Execute statement gagal (tujuan pasien): " . $stmt2->error);
		}

		// 4. Insert ke tabel penjamin
		$stmt3 = $mysqli->prepare("INSERT INTO pendaftaran.penjamin (JENIS, NOPEN, NOMOR) VALUES (?, ?, ?)");
		if (!$stmt3) {
			throw new Exception("Prepare statement gagal (penjamin): " . $mysqli->error);
		}
		$stmt3->bind_param('iss', $jenisJaminan, $nopen, $noSep);
		$stmt3->execute();

		if ($stmt3->error) {
			throw new Exception("Execute statement gagal (penjamin): " . $stmt3->error);
		}

		// Commit jika semua query berhasil
		$mysqli->commit();
	} catch (Exception $e) {
		echo 'rollback';
		// Rollback jika ada error
		$mysqli->rollback();
		echo "Gagal menyimpan data: " . $e->getMessage();
	}
	// Tutup koneksi
	$mysqli->close();

	// $sql_cek = "SELECT * FROM pendaftaran.pendaftaran WHERE NORM='$nomr' AND STATUS=1";
	$query_check2 = mysqli_query($conn, $sql_cek);
	if($query_check2){
		$count = mysqli_num_rows($query_check2);
		if($count > 0){ ?>
			<script type="text/javascript">
				var sep = '<?= $noSep; ?>';
				var cetak = "bpjs.CetakSEP_1";
				var poli = '<?=$poliTujuan?>';
				var ruangan = '<?=$ruangan?>';
				// if(poli=='THT') {
				// 	cetak="bpjs.CetakSEP_1";
				// }
				if(ruangan != '105120101') {
					window.requestPrint({
						NAME:cetak,
						TYPE:"Word",
						EXT:"docx",
						PARAMETER:{
							PSEP:sep,CETAK_HEADER:1
						},
						REQUEST_FOR_PRINT:true,
						PRINT_NAME:"CetakSEP",
						CONNECTION_NUMBER:0,
						COPIES:1,
						id:"data.model.RequestReport-1"
					});

					document.addEventListener("DOMContentLoaded", function(event) {
						swal({
							icon : 'success',
							title : 'Sukses!',
							text: 'Mohon cek kembali kesesuaian data di SEP. Segera ke helpdesk jika ada kesalahan.',
							timer: 8000,
							button : false
						}).then(function() {
							window.location = "index.php";
						});
					});
					$(window).load(function() {
						$(".se-pre-con").fadeOut("slow");
					});
				} else {
					document.addEventListener("DOMContentLoaded", function(event) {
						swal({
							icon : 'success',
							title : 'Sukses!',
							text: 'Kiosk sudah tidak mencetak Bukti Registrasi, anda dapat melihatnya melalui RSKD Mobile. Segera ke helpdesk jika ada kesalahan.',
							timer: 8000,
							button : false
						}).then(function() {
							window.location = "index.php";
						});
					});
					$(window).load(function() {
						$(".se-pre-con").fadeOut("slow");
					});
				}
			</script>
		<?php 
		}
	} 
}